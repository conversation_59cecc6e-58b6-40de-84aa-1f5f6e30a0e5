"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour,\n            type: \"timeslot\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\", isOver && \"bg-blue-50\"),\n        onDoubleClick: ()=>onDoubleClick(0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 124,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 134,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c1 = WeekView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});