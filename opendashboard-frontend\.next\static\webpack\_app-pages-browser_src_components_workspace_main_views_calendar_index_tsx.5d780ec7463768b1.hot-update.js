"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { hour, date, onDoubleClick, children, dropIndicator } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(date, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: date,\n            hour: hour,\n            type: \"timeslot\"\n        }\n    });\n    const showIndicator = dropIndicator && (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(dropIndicator.date, date) && new Date(dropIndicator.date).getHours() === hour;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative min-h-[60px] cursor-pointer\", isOver && \"bg-blue-100\"),\n        style: {\n            height: \"60px\"\n        },\n        onDoubleClick: ()=>onDoubleClick(0),\n        children: [\n            showIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute w-full bg-blue-500 opacity-75 pointer-events-none\",\n                style: {\n                    height: \"2px\",\n                    top: \"\".concat(dropIndicator.minute / 60 * 100, \"%\"),\n                    zIndex: 50\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData, dropIndicator } = param;\n    _s1();\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize event segments to prevent unnecessary recalculations\n    const daySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentsForDay)(allSegments, selectedDate);\n    }, [\n        events,\n        selectedDate\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getAllDaySegments)(daySegments), [\n        daySegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getTimeSlotSegments)(daySegments), [\n        daySegments\n    ]);\n    // Calculate layout for overlapping segments\n    const { segmentLayouts } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_6__.calculateLayout)(timeSlotSegments);\n    }, [\n        timeSlotSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate) ? {\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        selectedDate\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_4__.NoEvents, {\n            title: \"No events scheduled\",\n            message: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"\".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \" is completely free.\"),\n            showCreateButton: canEditData,\n            onCreate: ()=>{\n                const newDate = new Date(selectedDate);\n                newDate.setHours(9, 0, 0, 0);\n                openAddEventForm(newDate);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 121,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 overflow-auto relative bg-white\",\n            id: \"day-view-container\",\n            children: [\n                hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                        style: {\n                            height: \"60px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                \"data-time-labels\": \"true\",\n                                className: \"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-semibold\",\n                                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate, hour), \"h\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black opacity-60\",\n                                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate, hour), \"a\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                hour: hour,\n                                date: selectedDate,\n                                dropIndicator: dropIndicator,\n                                onDoubleClick: (minute)=>{\n                                    if (canEditData) {\n                                        const newDate = new Date(selectedDate);\n                                        newDate.setHours(hour, minute, 0, 0);\n                                        openAddEventForm(newDate);\n                                    }\n                                },\n                                children: segmentLayouts.map((layout)=>{\n                                    var _activeDragData_payload, _activeDragData_payload1;\n                                    const segmentStart = layout.segment.startTime;\n                                    const isFirstHour = segmentStart.getHours() === hour;\n                                    if (!isFirstHour) return null;\n                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentHeight)(layout.segment);\n                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_5__.getSegmentTopOffset)(layout.segment);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                        segment: layout.segment,\n                                        style: {\n                                            height: \"\".concat(segmentHeight, \"px\"),\n                                            position: \"absolute\",\n                                            top: \"\".concat(topOffset, \"px\"),\n                                            left: \"\".concat(layout.left, \"%\"),\n                                            width: \"\".concat(layout.width, \"%\"),\n                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                            paddingRight: \"2px\",\n                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                        },\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const container = document.getElementById(\"day-view-container\");\n                                            if (container) {\n                                                savedScrollTop.current = container.scrollTop;\n                                            }\n                                            setSelectedEvent(layout.segment.originalEventId);\n                                            handleEventClick(layout.segment.originalEvent);\n                                        },\n                                        view: \"day\",\n                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                    }, layout.segment.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)),\n                currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\",\n                    style: {\n                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                        right: \"4px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 137,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold text-black mb-1 text-xs\",\n                        children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"EEEE\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                        children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate, \"d\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            daySegments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_7__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"day\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined),\n            daySegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayView, \"rKQaW4qzJjohXAHqKTEXOPEYpLE=\");\n_c1 = DayView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});