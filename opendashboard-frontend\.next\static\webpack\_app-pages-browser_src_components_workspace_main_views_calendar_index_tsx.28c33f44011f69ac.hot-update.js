"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx":
/*!***************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx ***!
  \***************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventItem: function() { return /* binding */ CalendarEventItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, onClick, onContextMenu, view = \"month\", isDragging, showTitle = true, isDraggable = true } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isMultiDay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.isMultiDayEvent)(event), [\n        event\n    ]);\n    const isAllDay = event.isAllDay;\n    // Disable dragging for multi-day and all-day events\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable)({\n        id: \"event-\".concat(event.id),\n        data: {\n            type: \"event\",\n            payload: event\n        },\n        disabled: !isDraggable || isMultiDay || isAllDay\n    });\n    // Combine external isDragging with internal dndIsDragging\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const start = new Date(event.start);\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        return {\n            start,\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            isInstant: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.isInstantEvent)(event),\n            formattedTime: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(start, view, {\n                shortFormat: true\n            })\n        };\n    }, [\n        event,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            ...style,\n            backgroundColor: opaqueBackground,\n            minHeight: view === \"month\" ? \"24px\" : \"30px\",\n            marginBottom: view === \"month\" ? \"4px\" : \"0px\",\n            // Add subtle shadow for better visual depth\n            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n            opacity: combinedIsDragging ? 0.5 : 1\n        };\n    }, [\n        style,\n        view,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && !isMultiDay && !isAllDay && \"cursor-pointer\", (isMultiDay || isAllDay || !isDraggable) && \"cursor-default\", \"p-1\"),\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", \"flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0\", \"text-[0.65rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && !isMultiDay && !isAllDay && \"cursor-pointer\", (isMultiDay || isAllDay || !isDraggable) && \"cursor-default\", \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\")\n        };\n    }, [\n        eventDetails,\n        view,\n        combinedIsDragging,\n        isDraggable,\n        isMultiDay,\n        isAllDay\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, undefined),\n                    showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views for medium/large events\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: event.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined),\n                showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: eventDetails.formattedTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(event.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: eventStyles,\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...isDraggable ? {\n            ...listeners,\n            ...attributes\n        } : {},\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventItem, \"IW84WJgOpp54x8rwdqNNTQV/zmM=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable\n    ];\n});\n_c = CalendarEventItem;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\n"));

/***/ })

});