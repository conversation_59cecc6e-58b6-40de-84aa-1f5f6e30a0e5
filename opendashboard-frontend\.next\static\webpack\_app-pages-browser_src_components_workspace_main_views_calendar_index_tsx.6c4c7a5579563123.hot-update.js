"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx":
/*!***************************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx ***!
  \***************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarEventItem: function() { return /* binding */ CalendarEventItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_color__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/color */ \"(app-pages-browser)/./src/utils/color.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Helper function to check if an event is multi-day\nconst isMultiDayEvent = (event)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    // Reset time to compare dates only\n    const startDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());\n    const endDate = new Date(eventEnd.getFullYear(), eventEnd.getMonth(), eventEnd.getDate());\n    return startDate.getTime() !== endDate.getTime();\n};\nconst CalendarEventItem = (param)=>{\n    let { event, style, onClick, onContextMenu, view = \"month\", isDragging, showTitle = true, isDraggable = true } = param;\n    _s();\n    const dragRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isMultiDay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>isMultiDayEvent(event), [\n        event\n    ]);\n    const isAllDay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return start.getHours() === 0 && start.getMinutes() === 0 && end.getHours() === 23 && end.getMinutes() === 59;\n    }, [\n        event\n    ]);\n    // Disable dragging for multi-day and all-day events\n    const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable)({\n        id: \"event-\".concat(event.id),\n        data: {\n            type: \"event\",\n            payload: event\n        },\n        disabled: !isDraggable || isMultiDay || isAllDay\n    });\n    // Combine external isDragging with internal dndIsDragging\n    const combinedIsDragging = isDragging || dndIsDragging;\n    // Memoize event calculations\n    const eventDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const start = new Date(event.start);\n        const eventHeight = (style === null || style === void 0 ? void 0 : style.height) ? parseInt(style.height.toString().replace(\"px\", \"\")) : null;\n        return {\n            start,\n            eventSize: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventSize)(eventHeight),\n            isInstant: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.isInstantEvent)(event),\n            formattedTime: (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.formatEventTime)(start, view, {\n                shortFormat: true\n            })\n        };\n    }, [\n        event,\n        style,\n        view\n    ]);\n    // Memoize styling\n    const eventStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const denimColorInfo = (0,_utils_color__WEBPACK_IMPORTED_MODULE_3__.ColorInfo)(\"Denim\");\n        // Extract RGB values from the rgba string and make it fully opaque\n        const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\n        const opaqueBackground = rgbMatch ? \"rgb(\".concat(rgbMatch[1], \", \").concat(rgbMatch[2], \", \").concat(rgbMatch[3], \")\") : denimColorInfo.bg;\n        return {\n            ...style,\n            backgroundColor: opaqueBackground,\n            minHeight: view === \"month\" ? \"24px\" : \"30px\",\n            marginBottom: view === \"month\" ? \"4px\" : \"0px\",\n            // Add subtle shadow for better visual depth\n            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)\",\n            opacity: combinedIsDragging ? 0.5 : 1\n        };\n    }, [\n        style,\n        view,\n        combinedIsDragging\n    ]);\n    // Memoize classes\n    const eventClasses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return {\n                baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && !isMultiDay && !isAllDay && \"cursor-pointer\", (isMultiDay || isAllDay || !isDraggable) && \"cursor-default\", \"p-1\"),\n                containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", \"flex-nowrap\"),\n                titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\", \"max-w-[70%]\"),\n                timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs flex-shrink-0\", \"text-[0.65rem]\")\n            };\n        }\n        // Day and Week views for medium/large events\n        return {\n            baseClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md select-none text-black text-xs overflow-hidden\", !combinedIsDragging && isDraggable && !isMultiDay && !isAllDay && \"cursor-pointer\", (isMultiDay || isAllDay || !isDraggable) && \"cursor-default\", \"p-2\"),\n            containerClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col\", \"space-y-0.5\"),\n            titleClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-medium truncate leading-tight text-xs overflow-hidden\"),\n            timeClasses: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-75 text-xs\")\n        };\n    }, [\n        eventDetails,\n        view,\n        combinedIsDragging,\n        isDraggable,\n        isMultiDay,\n        isAllDay\n    ]);\n    // Render event content based on view and size\n    const renderEventContent = ()=>{\n        // Month view or small events\n        if (view === \"month\" || eventDetails.eventSize === \"small\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: eventClasses.containerClasses,\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.titleClasses,\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 13\n                    }, undefined),\n                    showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: eventClasses.timeClasses,\n                        children: eventDetails.formattedTime\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Day and Week views for medium/large events\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: eventClasses.containerClasses,\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.titleClasses,\n                    children: event.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, undefined),\n                showTitle && eventDetails.formattedTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: eventClasses.timeClasses,\n                    children: eventDetails.formattedTime\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"event-\".concat(event.id),\n        ref: (node)=>{\n            setNodeRef(node);\n            dragRef.current = node;\n        },\n        style: eventStyles,\n        className: eventClasses.baseClasses,\n        onClick: onClick,\n        onContextMenu: onContextMenu,\n        ...isDraggable ? {\n            ...listeners,\n            ...attributes\n        } : {},\n        children: renderEventContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\CalendarEventItem.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventItem, \"uBmHTFTi4++3+BrngoJLagJ4P74=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDraggable\n    ];\n});\n_c = CalendarEventItem;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\n"));

/***/ })

});