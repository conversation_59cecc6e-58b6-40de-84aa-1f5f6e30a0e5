"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/workspace/main/common/searchmodal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SearchModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _views_viewIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HighlightedContent = (param)=>{\n    let { content, highlight, query } = param;\n    if (!content) return null;\n    if (query && query.trim()) {\n        const searchTerm = query.trim().toLowerCase();\n        const contentLower = content.toLowerCase();\n        const parts = [];\n        let lastIndex = 0;\n        let currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        if (currentIndex === -1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: content\n        }, void 0, false);\n        while(currentIndex !== -1){\n            parts.push(content.substring(lastIndex, currentIndex));\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200\",\n                children: content.substring(currentIndex, currentIndex + searchTerm.length)\n            }, currentIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined));\n            lastIndex = currentIndex + searchTerm.length;\n            currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        }\n        if (lastIndex < content.length) {\n            parts.push(content.substring(lastIndex));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: parts\n        }, void 0, false);\n    }\n    if (highlight && highlight.start >= 0) {\n        const start = Math.max(0, highlight.start);\n        const end = Math.min(content.length, highlight.end);\n        if (start < end && start < content.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    content.substring(0, start),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                        className: \"bg-yellow-200\",\n                        children: content.substring(start, end)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined),\n                    content.substring(end)\n                ]\n            }, void 0, true);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: content\n    }, void 0, false);\n};\n_c = HighlightedContent;\nconst groupResults = (results)=>{\n    const grouped = {\n        databases: [],\n        pages: [],\n        views: [],\n        documents: [],\n        members: []\n    };\n    results.forEach((result)=>{\n        if (result.image || !result.source) {\n            grouped.members.push(result);\n        } else if (result.source) {\n            if (result.source.databaseId && !result.source.viewId) grouped.databases.push(result);\n            else if (result.source.viewId) grouped.views.push(result);\n            else if (result.source.documentId) grouped.documents.push(result);\n            else if (result.source.pageId) grouped.pages.push(result);\n        }\n    });\n    return grouped;\n};\nfunction SearchModal(param) {\n    let { onClose, debounceTimeoutMS = 2500 } = param;\n    var _workspace_workspace, _workspace_workspace1;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentSearches, setRecentSearches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1), [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)(), { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)(), router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const workspaceId = workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id;\n    const resultsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedSearches = localStorage.getItem(\"recentSearches\");\n        if (savedSearches) setRecentSearches(JSON.parse(savedSearches));\n    }, []);\n    const getErrorMessage = (error)=>{\n        if (error instanceof Error) {\n            if (\"response\" in error) {\n                var _apiError_response_data, _apiError_response;\n                const apiError = error;\n                return ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : (_apiError_response_data = _apiError_response.data) === null || _apiError_response_data === void 0 ? void 0 : _apiError_response_data.message) || apiError.message;\n            }\n            return error.message;\n        }\n        return \"An unexpected error occurred\";\n    };\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(searchQuery) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, append = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (!searchQuery.trim() || !workspaceId || !token) {\n            setResults([]);\n            setError(null);\n            setIsLoading(false);\n            setHasMore(false);\n            setTotalItems(0);\n            setCurrentPage(1);\n            return;\n        }\n        const currentSearchQuery = searchQuery;\n        if (page === 1) setHasMore(true);\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_6__.searchWorkspaces)(token.token, workspaceId, currentSearchQuery, page, 25);\n            if (!response.isSuccess) throw new Error(response.error || \"Search failed\");\n            const newResults = response.data.data.results.results || [];\n            const pagination = response.data.data.results.pagination;\n            if (currentSearchQuery === query) {\n                setTotalItems(pagination.totalItems);\n                setHasMore(page < pagination.totalPages);\n                setResults(append ? (prev)=>[\n                        ...prev,\n                        ...newResults\n                    ] : newResults);\n                setCurrentPage(page);\n                setCurrentQuery(currentSearchQuery);\n            }\n        } catch (error) {\n            console.error(\"Search error:\", error);\n            if (!append && currentSearchQuery === query) {\n                setError(getErrorMessage(error));\n            }\n        } finally{\n            if (currentSearchQuery === query) {\n                setIsLoading(false);\n            }\n        }\n    }, [\n        workspaceId,\n        token,\n        query\n    ]);\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default()((searchQuery)=>{\n        if (searchQuery.trim()) {\n            setCurrentPage(1);\n            performSearch(searchQuery, 1, false);\n        }\n    }, debounceTimeoutMS), [\n        performSearch,\n        debounceTimeoutMS,\n        setCurrentPage\n    ]);\n    const loadMore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!isLoading && hasMore && currentQuery) {\n            performSearch(currentQuery, currentPage + 1, true);\n        }\n    }, [\n        isLoading,\n        hasMore,\n        currentQuery,\n        currentPage,\n        performSearch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!resultsContainerRef.current) return;\n        const observer = new IntersectionObserver((entries)=>{\n            const [entry] = entries;\n            if (entry.isIntersecting && hasMore && !isLoading && results.length > 0) {\n                loadMore();\n            }\n        }, {\n            root: resultsContainerRef.current,\n            rootMargin: \"0px 0px 200px 0px\",\n            threshold: 0.1\n        });\n        const sentinel = document.getElementById(\"search-results-sentinel\");\n        if (sentinel) {\n            observer.observe(sentinel);\n        }\n        return ()=>{\n            if (sentinel) {\n                observer.unobserve(sentinel);\n            }\n            observer.disconnect();\n        };\n    }, [\n        loadMore,\n        hasMore,\n        isLoading,\n        results.length\n    ]);\n    // Handle search input changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        debouncedSearch.cancel();\n        setResults([]);\n        setCurrentPage(1);\n        setHasMore(false);\n        setTotalItems(0);\n        setCurrentQuery(\"\");\n        if (query.trim()) {\n            setIsLoading(true);\n            debouncedSearch(query);\n        } else {\n            setIsLoading(false);\n        }\n        return ()=>debouncedSearch.cancel();\n    }, [\n        query,\n        debouncedSearch\n    ]);\n    const handleResultClick = (result)=>{\n        router.push(url(result.path));\n        onClose();\n        saveRecentSearch(query);\n    };\n    const saveRecentSearch = (search)=>{\n        if (search.trim()) {\n            const updatedSearches = [\n                search,\n                ...recentSearches.filter((s)=>s !== search)\n            ].slice(0, 5);\n            setRecentSearches(updatedSearches);\n            localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n        }\n    };\n    const deleteRecentSearch = (search, e)=>{\n        e.stopPropagation();\n        const updatedSearches = recentSearches.filter((s)=>s !== search);\n        setRecentSearches(updatedSearches);\n        localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n    };\n    const getIconForSource = (result)=>{\n        var _result_path, _result_path1, _result_source, _result_source1, _result_source2, _result_source3;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.ClockThreeIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 243,\n            columnNumber: 57\n        }, this);\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.NoteIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 244,\n            columnNumber: 53\n        }, this);\n        if (result.viewType) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_viewIcon__WEBPACK_IMPORTED_MODULE_11__.ViewIcon, {\n            type: result.viewType,\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 246,\n            columnNumber: 33\n        }, this);\n        if (((_result_source = result.source) === null || _result_source === void 0 ? void 0 : _result_source.databaseId) && result.source.recordId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.SquareListIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 248,\n            columnNumber: 69\n        }, this);\n        if ((_result_source1 = result.source) === null || _result_source1 === void 0 ? void 0 : _result_source1.databaseId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TableIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 249,\n            columnNumber: 43\n        }, this);\n        if ((_result_source2 = result.source) === null || _result_source2 === void 0 ? void 0 : _result_source2.documentId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-accent-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 250,\n            columnNumber: 43\n        }, this);\n        if ((_result_source3 = result.source) === null || _result_source3 === void 0 ? void 0 : _result_source3.pageId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-secondary-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 251,\n            columnNumber: 39\n        }, this);\n        if (result.image !== undefined || !result.source) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.UserGroupIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 253,\n            columnNumber: 62\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n    const getResultType = (result)=>{\n        var _result_path, _result_path1;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return \"Reminder\";\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return \"Note\";\n        if (result.image) return \"Member\";\n        if (!result.source) return \"Member\";\n        if (result.source.databaseId && result.viewType) {\n            switch(result.viewType){\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Table:\n                    return \"Table View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Board:\n                    return \"Board View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Dashboard:\n                    return \"Dashboard\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Document:\n                    return \"Document View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Form:\n                    return \"Form View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.SummaryTable:\n                    return \"Summary Table\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.ListView:\n                    return \"List View\";\n                default:\n                    return \"Database View\";\n            }\n        }\n        if (result.source.databaseId && result.source.recordId) return \"Record\";\n        if (result.source.databaseId) return \"Database\";\n        if (result.source.documentId) return \"Document\";\n        if (result.source.pageId) return \"Page\";\n        return \"Document\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: true,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"p-0 gap-0 w-[95vw] max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden sm:w-full max-h-[90vh]\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center border-b px-2 sm:px-3 relative bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                            className: \"mr-2 h-4 w-4 shrink-0 text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            className: \"flex h-10 sm:h-12 rounded-md border-0 bg-transparent py-1.5 sm:py-2 text-xs outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50\",\n                            placeholder: \"Search \".concat((workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.name) || \"workspace\", \"...\"),\n                            value: query,\n                            onChange: (e)=>setQuery(e.target.value),\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[1px] text-gray-400 ml-2\",\n                            children: \"ESC to close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] sm:max-h-[65vh] overflow-y-auto pr-1\",\n                    ref: resultsContainerRef,\n                    children: [\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed top-0 left-0 right-0 h-0.5 z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-500/30 h-full animate-pulse\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        isLoading && currentPage === 1 && results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                    className: \"inline-block w-5 h-5 sm:w-6 sm:h-6 text-gray-600 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 sm:mt-3 text-xs text-gray-600\",\n                                    children: \"Searching workspace...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.WifiSlashIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"Network Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-gray-500\",\n                                    children: \"Please check your internet connection and try again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this) : !query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 py-0.5 bg-gray-50\",\n                                    children: \"Recent Searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                recentSearches.length > 0 ? recentSearches.map((search, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 rounded transition-colors\",\n                                        onClick: ()=>setQuery(search),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TimerIcon, {\n                                                        className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-700\",\n                                                        children: search\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>deleteRecentSearch(search, e),\n                                                className: \"p-1 rounded-full hover:bg-gray-100 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                                    className: \"h-3 w-3 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-3 text-xs text-gray-500\",\n                                    children: \"No recent searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this) : results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"No results found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-gray-500\",\n                                    children: [\n                                        \"We couldn't find anything matching \\\"\",\n                                        query,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this) : Object.entries(groupResults(results)).map((param)=>{\n                            let [category, items] = param;\n                            return items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 sm:px-3 py-0.5 sm:py-1 bg-gray-50\",\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 19\n                                    }, this),\n                                    items.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 sm:px-3 py-1.5 sm:py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-start gap-1.5 sm:gap-3\",\n                                            onClick: ()=>handleResultClick(result),\n                                            children: [\n                                                result.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: result.image,\n                                                    alt: result.name || \"\",\n                                                    className: \"h-6 w-6 sm:h-7 sm:w-7 rounded-full object-cover mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5 p-1 sm:p-1.5 rounded-md sm:rounded-lg bg-gray-100 text-gray-600\",\n                                                    children: getIconForSource(result)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-baseline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                        content: result.title || result.name,\n                                                                        highlight: result.highlight,\n                                                                        query: query\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.publishedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 whitespace-nowrap hidden sm:inline\",\n                                                                    children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(result.publishedAt), {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-0.5 sm:mt-1 text-xs text-gray-500 line-clamp-1 sm:line-clamp-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                content: result.content,\n                                                                highlight: result.highlight,\n                                                                query: query\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        result.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 sm:mt-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-1 py-0.5 rounded text-[9px] font-medium bg-gray-100 text-gray-800\",\n                                                                children: getResultType(result)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, result.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, this))\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        isLoading && currentPage > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                className: \"inline-block w-3 h-3 sm:w-4 sm:h-4 text-gray-400 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"search-results-sentinel\",\n                            className: \"h-4 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        !isLoading && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: hasMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Scroll for more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: totalItems > 0 ? \"Showing all \".concat(totalItems, \" results\") : \"No more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchModal, \"VlhYsU+EfLYvri7d/SGldOdcNNU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c1 = SearchModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"HighlightedContent\");\n$RefreshReg$(_c1, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\n"));

/***/ })

});