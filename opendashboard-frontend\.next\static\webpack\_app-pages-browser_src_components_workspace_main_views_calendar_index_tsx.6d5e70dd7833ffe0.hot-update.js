"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour,\n            type: \"timeslot\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\", isOver && \"bg-blue-50\"),\n        onDoubleClick: ()=>onDoubleClick(0),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 124,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 134,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c1 = WeekView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});