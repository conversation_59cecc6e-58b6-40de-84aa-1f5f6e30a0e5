"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx":
/*!************************************************************!*\
  !*** ./src/components/workspace/main/views/list/index.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: function() { return /* binding */ ListView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/checkbox */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/checkbox.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _list_css__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./list.css */ \"(app-pages-browser)/./src/components/workspace/main/views/list/list.css\");\n/* __next_internal_client_entry_do_not_use__ ListView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListFieldRenderer = (param)=>{\n    let { field, row, databaseId } = param;\n    const meta = {\n        databaseId: databaseId,\n        column: field,\n        triggerEdit: false,\n        headerLocked: true,\n        contentLocked: true\n    };\n    const renderProps = {\n        column: {\n            key: field.id,\n            __meta__: meta,\n            idx: 0,\n            name: field.title,\n            frozen: false,\n            resizable: false,\n            sortable: false,\n            width: 150,\n            minWidth: 50,\n            maxWidth: undefined,\n            cellClass: undefined,\n            headerCellClass: undefined,\n            editable: false\n        },\n        row: row,\n        rowIdx: 0,\n        tabIndex: -1,\n        onRowChange: ()=>{},\n        isCellSelected: false,\n        selectCell: ()=>{},\n        isRowSelected: false\n    };\n    let RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n    switch(field.type){\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_11__.AIRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UUID:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.UUIDRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Number:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Text:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Derived:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_13__.LinkedRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Summarize:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_15__.SummarizeRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_12__.SelectRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Checkbox:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_7__.CheckboxRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Date:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_8__.DateRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_9__.PersonRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_10__.FileRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ScannableCode:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_17__.ScannableCodeRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_16__.ButtonGroupRenderer;\n            break;\n        default:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n    }\n    // @ts-ignore\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererComponent, {\n        ...renderProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 135,\n        columnNumber: 12\n    }, undefined);\n};\n_c = ListFieldRenderer;\nconst ListView = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo;\n    _s();\n    const { databaseStore, databaseErrorStore, members, workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { cache, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViews)();\n    const { filter, sorts, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewSelection)();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_14__.usePage)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_19__.useMaybeShared)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.usePathname)();\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__.useStackedPeek)();\n    const contentScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const horizontalScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_20__.useMaybeTemplate)();\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    // Sync horizontal scrolling between content and scrollbar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const contentElement = contentScrollRef.current;\n        const scrollbarElement = horizontalScrollRef.current;\n        if (!contentElement || !scrollbarElement) return;\n        const syncContentScroll = ()=>{\n            scrollbarElement.scrollLeft = contentElement.scrollLeft;\n        };\n        const syncScrollbarScroll = ()=>{\n            contentElement.scrollLeft = scrollbarElement.scrollLeft;\n        };\n        contentElement.addEventListener(\"scroll\", syncContentScroll);\n        scrollbarElement.addEventListener(\"scroll\", syncScrollbarScroll);\n        return ()=>{\n            contentElement.removeEventListener(\"scroll\", syncContentScroll);\n            scrollbarElement.removeEventListener(\"scroll\", syncScrollbarScroll);\n        };\n    }, []);\n    const getFieldsToDisplay = ()=>{\n        const fieldsToDisplay = [];\n        if (!database) return fieldsToDisplay;\n        const dbDefinition = database.database.definition;\n        if (!dbDefinition) return fieldsToDisplay;\n        let { columnsOrder, columnPropsMap } = definition;\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : [];\n        columnPropsMap = columnPropsMap || {};\n        for (const key of dbDefinition.columnIds){\n            if (!columnsOrder.includes(key)) columnsOrder.push(key);\n            if (!columnPropsMap[key]) columnPropsMap[key] = {};\n        }\n        for (const id of columnsOrder){\n            const dbCol = dbDefinition.columnsMap[id];\n            if (!dbCol) continue;\n            if (columnPropsMap[id].isHidden) continue;\n            fieldsToDisplay.push(dbCol);\n        }\n        return fieldsToDisplay;\n    };\n    const getProcessedRows = ()=>{\n        if (!database) return [];\n        const sortOptions = [];\n        if (sorts.length > 0) {\n            sortOptions.push(...sorts);\n        } else if (definition.sorts.length > 0) {\n            sortOptions.push(...definition.sorts);\n        }\n        if (sortOptions.length === 0) sortOptions.push({\n            columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.MagicColumn.CreatedAt,\n            order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Sort.Asc\n        });\n        const colIds = cache.getCache(\"newlyCreatedRecords\");\n        const createdColIds = colIds && Array.isArray(colIds) ? colIds : [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__.filterAndSortRecords)(database, members, databaseStore, definition.filter, filter, sortOptions, workspace.workspaceMember.userId, \"\", maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.id, createdColIds);\n        return rows;\n    };\n    const filteredRows = getProcessedRows();\n    const rows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__.searchFilteredRecords)(search, filteredRows);\n    const fieldsToDisplay = getFieldsToDisplay();\n    if (!database) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                lineNumber: 255,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 254,\n            columnNumber: 13\n        }, undefined);\n    }\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__.getDatabaseTitleCol)(database.database);\n    const isInRecordTab = !!maybeRecord;\n    const currentRecordId = maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id;\n    const isOnRecordPage = pathname.includes(\"/records/\") && pathname.endsWith(\"/records/\".concat(currentRecordId));\n    const handleRecordClick = (recordId, recordDatabaseId)=>{\n        if (definition.lockContent) return;\n        openRecord(recordId, recordDatabaseId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-hidden listView\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-hidden size-full flex flex-col\",\n            children: [\n                !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 border-b bg-yellow-50 text-xs text-center border-neutral-300 font-medium\",\n                    children: \"Content is locked, record navigation is disabled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden scroll-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"content-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: contentScrollRef,\n                                className: \"content-horizontal-scroll\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"scroll-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-b rowGrid border-neutral-200 header-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold bg-white check !w-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold bg-white fluid\",\n                                                    children: \"Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-black font-bold bg-white\",\n                                                        children: field.title\n                                                    }, field.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 41\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        rows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No records found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 37\n                                        }, undefined) : rows.map((row)=>{\n                                            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts, database.database, members);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rowGrid border-b \".concat(definition.lockContent ? \"cursor-default\" : \"hover:bg-neutral-100 cursor-pointer\"),\n                                                onClick: (e)=>{\n                                                    const target = e.target;\n                                                    const isInteractiveField = target.closest('.r-button-group, .r-scannable-code, .r-files, button, [role=\"button\"]');\n                                                    console.log(\"Row click:\", {\n                                                        target: target.tagName,\n                                                        isInteractiveField,\n                                                        className: target.className\n                                                    });\n                                                    if (!isInteractiveField) {\n                                                        handleRecordClick(row.record.id, row.record.databaseId);\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs check !w-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs flex flex-col fluid\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"title-text text-xs font-semibold\",\n                                                                children: title || \"Untitled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 text-xs text-muted-foreground pt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: database.database.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_22__.timeAgo)(new Date(row.updatedAt))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListFieldRenderer, {\n                                                                field: field,\n                                                                row: row,\n                                                                databaseId: definition.databaseId,\n                                                                isPublishedView: isPublishedView,\n                                                                lockContent: definition.lockContent || false\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, field.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 53\n                                                        }, undefined))\n                                                ]\n                                            }, row.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: horizontalScrollRef,\n                            className: \"horizontal-scroll-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"horizontal-scroll-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rowGrid\",\n                                    style: {\n                                        visibility: \"hidden\",\n                                        height: \"1px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"check !w-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"fluid\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, field.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 277,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 276,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ListView, \"Ynk7SE4baYX8dCi41vGxGC0O2TY=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewSelection,\n        _providers_page__WEBPACK_IMPORTED_MODULE_14__.usePage,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_19__.useMaybeShared,\n        _providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord,\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.usePathname,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__.useStackedPeek,\n        _providers_template__WEBPACK_IMPORTED_MODULE_20__.useMaybeTemplate\n    ];\n});\n_c1 = ListView;\nvar _c, _c1;\n$RefreshReg$(_c, \"ListFieldRenderer\");\n$RefreshReg$(_c1, \"ListView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx\n"));

/***/ })

});