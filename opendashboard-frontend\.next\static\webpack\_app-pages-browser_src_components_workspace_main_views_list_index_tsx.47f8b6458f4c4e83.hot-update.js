"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0,\n        showAbove: false\n    });\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    // Calculate popup position based on caret position\n    function calculatePopupPosition() {\n        if (!divRef.current || !atRange) {\n            return {\n                top: 0,\n                left: 0,\n                showAbove: false\n            };\n        }\n        const inputRect = divRef.current.getBoundingClientRect();\n        let caretRect = atRange.getBoundingClientRect();\n        // If caret rect is empty, create a temporary element to get position\n        if (caretRect.width === 0 && caretRect.height === 0) {\n            const tempRange = atRange.cloneRange();\n            const marker = document.createElement(\"span\");\n            marker.textContent = \"​\"; // zero-width space\n            tempRange.insertNode(marker);\n            caretRect = marker.getBoundingClientRect();\n            marker.remove();\n        }\n        const dropdownHeight = 250; // approximate dropdown height\n        const spaceBelow = window.innerHeight - caretRect.bottom;\n        const spaceAbove = caretRect.top;\n        // Determine if we should show above or below\n        const showAbove = spaceBelow < dropdownHeight && spaceAbove > dropdownHeight;\n        // Calculate position relative to the input container\n        const left = Math.max(0, caretRect.left - inputRect.left);\n        const top = showAbove ? caretRect.top - inputRect.bottom - 2 // Show above the input\n         : caretRect.bottom - inputRect.top + 2; // Show below the caret\n        return {\n            top,\n            left,\n            showAbove\n        };\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Update popup position when mention mode is activated or search changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            const updatePosition = ()=>{\n                const newPosition = calculatePopupPosition();\n                setPopupPosition(newPosition);\n            };\n            // Initial positioning\n            setTimeout(updatePosition, 0);\n            // Update position on window resize or scroll\n            const handleResize = ()=>updatePosition();\n            const handleScroll = ()=>updatePosition();\n            window.addEventListener(\"resize\", handleResize);\n            window.addEventListener(\"scroll\", handleScroll, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", handleResize);\n                window.removeEventListener(\"scroll\", handleScroll, true);\n            };\n        }\n    }, [\n        mentionMode,\n        atRange\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    const dropdownStyle = popupPosition.showAbove ? {\n        bottom: \"100%\",\n        left: \"\".concat(popupPosition.left, \"px\"),\n        marginBottom: \"2px\",\n        position: \"absolute\"\n    } : {\n        top: \"100%\",\n        left: \"\".concat(popupPosition.left, \"px\"),\n        marginTop: \"2px\",\n        position: \"absolute\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 590,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"absolute z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: dropdownStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 605,\n                columnNumber: 17\n            }, this),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n                defaultOpen: true,\n                onOpenChange: closeModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                    className: \"max-w-[600px] !rounded-none p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                className: \"font-bold\",\n                                children: [\n                                    \"Set default for \",\n                                    ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 py-4 pt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col flex-1 gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            className: \"text-xs font-medium leading-6 text-gray-900\",\n                                            children: [\n                                                'Current default: \"',\n                                                modalState.currentDefault,\n                                                '\" (leave blank to remove)'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"text\",\n                                            autoCapitalize: \"none\",\n                                            autoCorrect: \"off\",\n                                            autoComplete: \"workflow-input-name\",\n                                            value: draftDefault,\n                                            placeholder: \"Type new default...\",\n                                            onChange: (e)=>setDraftDefault(e.target.value),\n                                            className: \"rounded-none text-xs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: confirmDefault,\n                                        className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                        children: \"Confirm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 646,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 589,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"N5jhlfo6tY7B9wxzuW5iYKs+WkY=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});