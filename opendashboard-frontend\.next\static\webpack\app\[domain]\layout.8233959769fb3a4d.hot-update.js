"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/workspace/main/common/searchmodal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SearchModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _views_viewIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HighlightedContent = (param)=>{\n    let { content, highlight, query } = param;\n    if (!content) return null;\n    if (query && query.trim()) {\n        const searchTerm = query.trim().toLowerCase();\n        const contentLower = content.toLowerCase();\n        const parts = [];\n        let lastIndex = 0;\n        let currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        if (currentIndex === -1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: content\n        }, void 0, false);\n        while(currentIndex !== -1){\n            parts.push(content.substring(lastIndex, currentIndex));\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                className: \"bg-yellow-200\",\n                children: content.substring(currentIndex, currentIndex + searchTerm.length)\n            }, currentIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined));\n            lastIndex = currentIndex + searchTerm.length;\n            currentIndex = contentLower.indexOf(searchTerm, lastIndex);\n        }\n        if (lastIndex < content.length) {\n            parts.push(content.substring(lastIndex));\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: parts\n        }, void 0, false);\n    }\n    if (highlight && highlight.start >= 0) {\n        const start = Math.max(0, highlight.start);\n        const end = Math.min(content.length, highlight.end);\n        if (start < end && start < content.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    content.substring(0, start),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n                        className: \"bg-yellow-200\",\n                        children: content.substring(start, end)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined),\n                    content.substring(end)\n                ]\n            }, void 0, true);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: content\n    }, void 0, false);\n};\n_c = HighlightedContent;\nconst groupResults = (results)=>{\n    const grouped = {\n        databases: [],\n        pages: [],\n        views: [],\n        documents: [],\n        members: []\n    };\n    results.forEach((result)=>{\n        if (result.image || !result.source) {\n            grouped.members.push(result);\n        } else if (result.source) {\n            if (result.source.databaseId && !result.source.viewId) grouped.databases.push(result);\n            else if (result.source.viewId) grouped.views.push(result);\n            else if (result.source.documentId) grouped.documents.push(result);\n            else if (result.source.pageId) grouped.pages.push(result);\n        }\n    });\n    return grouped;\n};\nfunction SearchModal(param) {\n    let { onClose, debounceTimeoutMS = 2500 } = param;\n    var _workspace_workspace, _workspace_workspace1;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentSearches, setRecentSearches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1), [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)(), { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)(), router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const workspaceId = workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id;\n    const resultsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedSearches = localStorage.getItem(\"recentSearches\");\n        if (savedSearches) setRecentSearches(JSON.parse(savedSearches));\n    }, []);\n    const getErrorMessage = (error)=>{\n        if (error instanceof Error) {\n            if (\"response\" in error) {\n                var _apiError_response_data, _apiError_response;\n                const apiError = error;\n                return ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : (_apiError_response_data = _apiError_response.data) === null || _apiError_response_data === void 0 ? void 0 : _apiError_response_data.message) || apiError.message;\n            }\n            return error.message;\n        }\n        return \"An unexpected error occurred\";\n    };\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(searchQuery) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, append = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (!searchQuery.trim() || !workspaceId || !token) {\n            setResults([]);\n            setError(null);\n            setIsLoading(false);\n            setHasMore(false);\n            setTotalItems(0);\n            setCurrentPage(1);\n            return;\n        }\n        const currentSearchQuery = searchQuery;\n        if (page === 1) setHasMore(true);\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_6__.searchWorkspaces)(token.token, workspaceId, currentSearchQuery, page, 25);\n            if (!response.isSuccess) throw new Error(response.error || \"Search failed\");\n            const newResults = response.data.data.results.results || [];\n            const pagination = response.data.data.results.pagination;\n            if (currentSearchQuery === query) {\n                setTotalItems(pagination.totalItems);\n                setHasMore(page < pagination.totalPages);\n                setResults(append ? (prev)=>[\n                        ...prev,\n                        ...newResults\n                    ] : newResults);\n                setCurrentPage(page);\n                setCurrentQuery(currentSearchQuery);\n            }\n        } catch (error) {\n            console.error(\"Search error:\", error);\n            if (!append && currentSearchQuery === query) {\n                setError(getErrorMessage(error));\n            }\n        } finally{\n            if (currentSearchQuery === query) {\n                setIsLoading(false);\n            }\n        }\n    }, [\n        workspaceId,\n        token,\n        query\n    ]);\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_8___default()((searchQuery)=>{\n        if (searchQuery.trim()) {\n            setCurrentPage(1);\n            performSearch(searchQuery, 1, false);\n        }\n    }, debounceTimeoutMS), [\n        performSearch,\n        debounceTimeoutMS,\n        setCurrentPage\n    ]);\n    const loadMore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!isLoading && hasMore && currentQuery) {\n            performSearch(currentQuery, currentPage + 1, true);\n        }\n    }, [\n        isLoading,\n        hasMore,\n        currentQuery,\n        currentPage,\n        performSearch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!resultsContainerRef.current) return;\n        const observer = new IntersectionObserver((entries)=>{\n            const [entry] = entries;\n            if (entry.isIntersecting && hasMore && !isLoading && results.length > 0) {\n                loadMore();\n            }\n        }, {\n            root: resultsContainerRef.current,\n            rootMargin: \"0px 0px 200px 0px\",\n            threshold: 0.1\n        });\n        const sentinel = document.getElementById(\"search-results-sentinel\");\n        if (sentinel) {\n            observer.observe(sentinel);\n        }\n        return ()=>{\n            if (sentinel) {\n                observer.unobserve(sentinel);\n            }\n            observer.disconnect();\n        };\n    }, [\n        loadMore,\n        hasMore,\n        isLoading,\n        results.length\n    ]);\n    // Handle search input changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        debouncedSearch.cancel();\n        setResults([]);\n        setCurrentPage(1);\n        setHasMore(false);\n        setTotalItems(0);\n        setCurrentQuery(\"\");\n        if (query.trim()) {\n            setIsLoading(true);\n            debouncedSearch(query);\n        } else {\n            setIsLoading(false);\n        }\n        return ()=>debouncedSearch.cancel();\n    }, [\n        query,\n        debouncedSearch\n    ]);\n    const handleResultClick = (result)=>{\n        router.push(url(result.path));\n        onClose();\n        saveRecentSearch(query);\n    };\n    const saveRecentSearch = (search)=>{\n        if (search.trim()) {\n            const updatedSearches = [\n                search,\n                ...recentSearches.filter((s)=>s !== search)\n            ].slice(0, 5);\n            setRecentSearches(updatedSearches);\n            localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n        }\n    };\n    const deleteRecentSearch = (search, e)=>{\n        e.stopPropagation();\n        const updatedSearches = recentSearches.filter((s)=>s !== search);\n        setRecentSearches(updatedSearches);\n        localStorage.setItem(\"recentSearches\", JSON.stringify(updatedSearches));\n    };\n    const getIconForSource = (result)=>{\n        var _result_path, _result_path1, _result_source, _result_source1, _result_source2, _result_source3;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.ClockThreeIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 243,\n            columnNumber: 57\n        }, this);\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.NoteIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 244,\n            columnNumber: 53\n        }, this);\n        if (result.viewType) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_viewIcon__WEBPACK_IMPORTED_MODULE_11__.ViewIcon, {\n            type: result.viewType,\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 246,\n            columnNumber: 33\n        }, this);\n        if (((_result_source = result.source) === null || _result_source === void 0 ? void 0 : _result_source.databaseId) && result.source.recordId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.SquareListIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 248,\n            columnNumber: 69\n        }, this);\n        if ((_result_source1 = result.source) === null || _result_source1 === void 0 ? void 0 : _result_source1.databaseId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TableIcon, {\n            className: \"h-4 w-4 text-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 249,\n            columnNumber: 43\n        }, this);\n        if ((_result_source2 = result.source) === null || _result_source2 === void 0 ? void 0 : _result_source2.documentId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-accent-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 250,\n            columnNumber: 43\n        }, this);\n        if ((_result_source3 = result.source) === null || _result_source3 === void 0 ? void 0 : _result_source3.pageId) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-secondary-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 251,\n            columnNumber: 39\n        }, this);\n        if (result.image !== undefined || !result.source) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.UserGroupIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 253,\n            columnNumber: 62\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.BookIcon, {\n            className: \"h-4 w-4 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n    const getResultType = (result)=>{\n        var _result_path, _result_path1;\n        if ((_result_path = result.path) === null || _result_path === void 0 ? void 0 : _result_path.includes(\"?tab=reminders\")) return \"Reminder\";\n        if ((_result_path1 = result.path) === null || _result_path1 === void 0 ? void 0 : _result_path1.includes(\"?tab=notes\")) return \"Note\";\n        if (result.image) return \"Member\";\n        if (!result.source) return \"Member\";\n        if (result.source.databaseId && result.viewType) {\n            switch(result.viewType){\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Table:\n                    return \"Table View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Board:\n                    return \"Board View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Dashboard:\n                    return \"Dashboard\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Document:\n                    return \"Document View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.Form:\n                    return \"Form View\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.SummaryTable:\n                    return \"Summary Table\";\n                case opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_12__.ViewType.ListView:\n                    return \"List View\";\n                default:\n                    return \"Database View\";\n            }\n        }\n        if (result.source.databaseId && result.source.recordId) return \"Record\";\n        if (result.source.databaseId) return \"Database\";\n        if (result.source.documentId) return \"Document\";\n        if (result.source.pageId) return \"Page\";\n        return \"Document\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: true,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"p-0 gap-0 w-[95vw] max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden sm:w-full max-h-[90vh]\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center border-b px-2 sm:px-3 relative bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                            className: \"mr-2 h-4 w-4 shrink-0 text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            className: \"flex h-10 sm:h-12 rounded-md border-0 bg-transparent py-1.5 sm:py-2 text-xs outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50\",\n                            placeholder: \"Search \".concat((workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.name) || \"workspace\", \"...\"),\n                            value: query,\n                            onChange: (e)=>setQuery(e.target.value),\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] sm:max-h-[65vh] overflow-y-auto pr-1\",\n                    ref: resultsContainerRef,\n                    children: [\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed top-0 left-0 right-0 h-0.5 z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-500/30 h-full animate-pulse\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        isLoading && currentPage === 1 && results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                    className: \"inline-block w-5 h-5 sm:w-6 sm:h-6 text-gray-600 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 sm:mt-3 text-xs text-gray-600\",\n                                    children: \"Searching workspace...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.WifiSlashIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"Network Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-gray-500\",\n                                    children: \"Please check your internet connection and try again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this) : !query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 py-0.5 bg-gray-50\",\n                                    children: \"Recent Searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this),\n                                recentSearches.length > 0 ? recentSearches.map((search, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 rounded transition-colors\",\n                                        onClick: ()=>setQuery(search),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.TimerIcon, {\n                                                        className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-700\",\n                                                        children: search\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>deleteRecentSearch(search, e),\n                                                className: \"p-1 rounded-full hover:bg-gray-100 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.XmarkIcon, {\n                                                    className: \"h-3 w-3 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-3 text-xs text-gray-500\",\n                                    children: \"No recent searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this) : results.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 py-6 sm:py-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs font-medium text-gray-900\",\n                                    children: \"No results found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs text-gray-500\",\n                                    children: [\n                                        \"We couldn't find anything matching \\\"\",\n                                        query,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this) : Object.entries(groupResults(results)).map((param)=>{\n                            let [category, items] = param;\n                            return items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 sm:px-3 py-0.5 sm:py-1 bg-gray-50\",\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this),\n                                    items.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 sm:px-3 py-1.5 sm:py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-start gap-1.5 sm:gap-3\",\n                                            onClick: ()=>handleResultClick(result),\n                                            children: [\n                                                result.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: result.image,\n                                                    alt: result.name || \"\",\n                                                    className: \"h-6 w-6 sm:h-7 sm:w-7 rounded-full object-cover mt-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5 p-1 sm:p-1.5 rounded-md sm:rounded-lg bg-gray-100 text-gray-600\",\n                                                    children: getIconForSource(result)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-baseline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                        content: result.title || result.name,\n                                                                        highlight: result.highlight,\n                                                                        query: query\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.publishedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 whitespace-nowrap hidden sm:inline\",\n                                                                    children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(result.publishedAt), {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        result.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-0.5 sm:mt-1 text-xs text-gray-500 line-clamp-1 sm:line-clamp-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HighlightedContent, {\n                                                                content: result.content,\n                                                                highlight: result.highlight,\n                                                                query: query\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        result.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 sm:mt-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-1 py-0.5 rounded text-[9px] font-medium bg-gray-100 text-gray-800\",\n                                                                children: getResultType(result)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, result.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, this))\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, this);\n                        }),\n                        isLoading && currentPage > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.Loader, {\n                                className: \"inline-block w-3 h-3 sm:w-4 sm:h-4 text-gray-400 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"search-results-sentinel\",\n                            className: \"h-4 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        !isLoading && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 sm:py-3 text-center\",\n                            children: hasMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: \"Scroll for more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: totalItems > 0 ? \"Showing all \".concat(totalItems, \" results\") : \"No more results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\searchmodal.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchModal, \"VlhYsU+EfLYvri7d/SGldOdcNNU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c1 = SearchModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"HighlightedContent\");\n$RefreshReg$(_c1, \"SearchModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi9zZWFyY2htb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNnRTtBQUNGO0FBQ2pCO0FBQ087QUFDVjtBQUNRO0FBRVA7QUFDRztBQUNSO0FBQ3NKO0FBQ3RJO0FBQ1Y7QUFDbUI7QUFJL0QsTUFBTTBCLHFCQUFxQjtRQUFDLEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxLQUFLLEVBQzJCO0lBQ2hGLElBQUksQ0FBQ0YsU0FBUyxPQUFPO0lBRXJCLElBQUlFLFNBQVNBLE1BQU1DLElBQUksSUFBSTtRQUN6QixNQUFNQyxhQUFhRixNQUFNQyxJQUFJLEdBQUdFLFdBQVc7UUFDM0MsTUFBTUMsZUFBZU4sUUFBUUssV0FBVztRQUN4QyxNQUFNRSxRQUFRLEVBQUU7UUFDaEIsSUFBSUMsWUFBWTtRQUNoQixJQUFJQyxlQUFlSCxhQUFhSSxPQUFPLENBQUNOLFlBQVlJO1FBRXBELElBQUlDLGlCQUFpQixDQUFDLEdBQUcscUJBQU87c0JBQUdUOztRQUNuQyxNQUFPUyxpQkFBaUIsQ0FBQyxFQUFHO1lBQzFCRixNQUFNSSxJQUFJLENBQUNYLFFBQVFZLFNBQVMsQ0FBQ0osV0FBV0M7WUFDeENGLE1BQU1JLElBQUksZUFDUiw4REFBQ0U7Z0JBQXdCQyxXQUFVOzBCQUNoQ2QsUUFBUVksU0FBUyxDQUFDSCxjQUFjQSxlQUFlTCxXQUFXVyxNQUFNO2VBRHhETjs7Ozs7WUFJYkQsWUFBWUMsZUFBZUwsV0FBV1csTUFBTTtZQUM1Q04sZUFBZUgsYUFBYUksT0FBTyxDQUFDTixZQUFZSTtRQUNsRDtRQUVBLElBQUlBLFlBQVlSLFFBQVFlLE1BQU0sRUFBRTtZQUM5QlIsTUFBTUksSUFBSSxDQUFDWCxRQUFRWSxTQUFTLENBQUNKO1FBQy9CO1FBRUEscUJBQU87c0JBQUdEOztJQUNaO0lBRUEsSUFBSU4sYUFBYUEsVUFBVWUsS0FBSyxJQUFJLEdBQUc7UUFDckMsTUFBTUEsUUFBUUMsS0FBS0MsR0FBRyxDQUFDLEdBQUdqQixVQUFVZSxLQUFLO1FBQ3pDLE1BQU1HLE1BQU1GLEtBQUtHLEdBQUcsQ0FBQ3BCLFFBQVFlLE1BQU0sRUFBRWQsVUFBVWtCLEdBQUc7UUFHbEQsSUFBSUgsUUFBUUcsT0FBT0gsUUFBUWhCLFFBQVFlLE1BQU0sRUFBRTtZQUN6QyxxQkFDRTs7b0JBQUdmLFFBQVFZLFNBQVMsQ0FBQyxHQUFHSTtrQ0FDdEIsOERBQUNIO3dCQUFLQyxXQUFVO2tDQUFpQmQsUUFBUVksU0FBUyxDQUFDSSxPQUFPRzs7Ozs7O29CQUN6RG5CLFFBQVFZLFNBQVMsQ0FBQ087OztRQUd6QjtJQUNGO0lBQ0EscUJBQU87a0JBQUduQjs7QUFDWjtLQTdDTUQ7QUFvRE4sTUFBTXNCLGVBQWUsQ0FBQ0M7SUFDcEIsTUFBTUMsVUFBVTtRQUFFQyxXQUFXLEVBQUU7UUFBRUMsT0FBTyxFQUFFO1FBQUVDLE9BQU8sRUFBRTtRQUFFQyxXQUFXLEVBQUU7UUFBRUMsU0FBUyxFQUFFO0lBQUM7SUFDbEZOLFFBQVFPLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDZCxJQUFJQSxPQUFPQyxLQUFLLElBQUksQ0FBQ0QsT0FBT0UsTUFBTSxFQUFFO1lBQ2xDVCxRQUFRSyxPQUFPLENBQUNqQixJQUFJLENBQUNtQjtRQUN2QixPQUFPLElBQUlBLE9BQU9FLE1BQU0sRUFBRTtZQUN4QixJQUFJRixPQUFPRSxNQUFNLENBQUNDLFVBQVUsSUFBSSxDQUFDSCxPQUFPRSxNQUFNLENBQUNFLE1BQU0sRUFBRVgsUUFBUUMsU0FBUyxDQUFDYixJQUFJLENBQUNtQjtpQkFDekUsSUFBSUEsT0FBT0UsTUFBTSxDQUFDRSxNQUFNLEVBQUVYLFFBQVFHLEtBQUssQ0FBQ2YsSUFBSSxDQUFDbUI7aUJBQzdDLElBQUlBLE9BQU9FLE1BQU0sQ0FBQ0csVUFBVSxFQUFFWixRQUFRSSxTQUFTLENBQUNoQixJQUFJLENBQUNtQjtpQkFDckQsSUFBSUEsT0FBT0UsTUFBTSxDQUFDSSxNQUFNLEVBQUViLFFBQVFFLEtBQUssQ0FBQ2QsSUFBSSxDQUFDbUI7UUFDcEQ7SUFDRjtJQUNBLE9BQU9QO0FBQ1Q7QUFPZSxTQUFTYyxZQUFZLEtBQXVEO1FBQXZELEVBQUVDLE9BQU8sRUFBRUMsb0JBQW9CLElBQUksRUFBb0IsR0FBdkQ7UUFPZEMsc0JBZ01hQTs7SUF0TWpDLE1BQU0sQ0FBQ3RDLE9BQU91QyxTQUFTLEdBQUdwRSwrQ0FBUUEsQ0FBQyxLQUFLLENBQUNpRCxTQUFTb0IsV0FBVyxHQUFHckUsK0NBQVFBLENBQWlCLEVBQUU7SUFDM0YsTUFBTSxDQUFDc0UsV0FBV0MsYUFBYSxHQUFHdkUsK0NBQVFBLENBQUMsUUFBUSxDQUFDd0UsT0FBT0MsU0FBUyxHQUFHekUsK0NBQVFBLENBQWdCO0lBQy9GLE1BQU0sQ0FBQzBFLGdCQUFnQkMsa0JBQWtCLEdBQUczRSwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ2pFLE1BQU0sQ0FBQzRFLGFBQWFDLGVBQWUsR0FBRzdFLCtDQUFRQSxDQUFDLElBQUksQ0FBQzhFLFNBQVNDLFdBQVcsR0FBRy9FLCtDQUFRQSxDQUFDO0lBQ3BGLE1BQU0sQ0FBQ2dGLFlBQVlDLGNBQWMsR0FBR2pGLCtDQUFRQSxDQUFDLElBQUksQ0FBQ2tGLGNBQWNDLGdCQUFnQixHQUFHbkYsK0NBQVFBLENBQUM7SUFDNUYsTUFBTSxFQUFFbUUsU0FBUyxFQUFFaUIsR0FBRyxFQUFFLEdBQUc3RSxrRUFBWUEsSUFBSSxFQUFFOEUsS0FBSyxFQUFFLEdBQUc3RSx3REFBT0EsSUFBSThFLFNBQVM1RSwwREFBU0E7SUFDcEYsTUFBTTZFLGNBQWNwQixzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSwyQ0FBQUEscUJBQXNCcUIsRUFBRTtJQUM1QyxNQUFNQyxzQkFBc0J0Riw2Q0FBTUEsQ0FBd0I7SUFFMURGLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlGLGdCQUFnQkMsYUFBYUMsT0FBTyxDQUFDO1FBQzNDLElBQUlGLGVBQWVmLGtCQUFrQmtCLEtBQUtDLEtBQUssQ0FBQ0o7SUFDbEQsR0FBRyxFQUFFO0lBRUwsTUFBTUssa0JBQWtCLENBQUN2QjtRQUN2QixJQUFJQSxpQkFBaUJ3QixPQUFPO1lBQzFCLElBQUksY0FBY3hCLE9BQU87b0JBRWhCeUIseUJBQUFBO2dCQURQLE1BQU1BLFdBQVd6QjtnQkFDakIsT0FBT3lCLEVBQUFBLHFCQUFBQSxTQUFTQyxRQUFRLGNBQWpCRCwwQ0FBQUEsMEJBQUFBLG1CQUFtQkUsSUFBSSxjQUF2QkYsOENBQUFBLHdCQUF5QkcsT0FBTyxLQUFJSCxTQUFTRyxPQUFPO1lBQzdEO1lBQ0EsT0FBTzVCLE1BQU00QixPQUFPO1FBQ3RCO1FBQ0EsT0FBTztJQUNUO0lBRUEsTUFBTUMsZ0JBQWdCbkcsa0RBQVdBLENBQUMsZUFBT29HO1lBQXFCQyx3RUFBTyxHQUFHQywwRUFBUztRQUMvRSxJQUFJLENBQUNGLFlBQVl4RSxJQUFJLE1BQU0sQ0FBQ3lELGVBQWUsQ0FBQ0YsT0FBTztZQUNqRGhCLFdBQVcsRUFBRTtZQUFHSSxTQUFTO1lBQU9GLGFBQWE7WUFDN0NRLFdBQVc7WUFBUUUsY0FBYztZQUFJSixlQUFlO1lBQ3BEO1FBQ0Y7UUFFQSxNQUFNNEIscUJBQXFCSDtRQUUzQixJQUFJQyxTQUFTLEdBQUd4QixXQUFXO1FBQzNCUixhQUFhO1FBQ2JFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTXlCLFdBQVcsTUFBTXpGLGdFQUFnQkEsQ0FBQzRFLE1BQU1BLEtBQUssRUFBRUUsYUFBYWtCLG9CQUFvQkYsTUFBTTtZQUM1RixJQUFJLENBQUNMLFNBQVNRLFNBQVMsRUFBRSxNQUFNLElBQUlWLE1BQU1FLFNBQVMxQixLQUFLLElBQUk7WUFFM0QsTUFBTW1DLGFBQWFULFNBQVNDLElBQUksQ0FBQ0EsSUFBSSxDQUFDbEQsT0FBTyxDQUFDQSxPQUFPLElBQUksRUFBRTtZQUMzRCxNQUFNMkQsYUFBYVYsU0FBU0MsSUFBSSxDQUFDQSxJQUFJLENBQUNsRCxPQUFPLENBQUMyRCxVQUFVO1lBRXhELElBQUlILHVCQUF1QjVFLE9BQU87Z0JBQ2hDb0QsY0FBYzJCLFdBQVc1QixVQUFVO2dCQUNuQ0QsV0FBV3dCLE9BQU9LLFdBQVdDLFVBQVU7Z0JBRXZDeEMsV0FBV21DLFNBQVNNLENBQUFBLE9BQVE7MkJBQUlBOzJCQUFTSDtxQkFBVyxHQUFHQTtnQkFDdkQ5QixlQUFlMEI7Z0JBQ2ZwQixnQkFBZ0JzQjtZQUNsQjtRQUNGLEVBQUUsT0FBT2pDLE9BQWdCO1lBQ3ZCdUMsUUFBUXZDLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CLElBQUksQ0FBQ2dDLFVBQVVDLHVCQUF1QjVFLE9BQU87Z0JBQzNDNEMsU0FBU3NCLGdCQUFnQnZCO1lBQzNCO1FBQ0YsU0FBVTtZQUNSLElBQUlpQyx1QkFBdUI1RSxPQUFPO2dCQUNoQzBDLGFBQWE7WUFDZjtRQUNGO0lBQ0YsR0FBRztRQUFDZ0I7UUFBYUY7UUFBT3hEO0tBQU07SUFFOUIsTUFBTW1GLGtCQUFrQjlHLGtEQUFXQSxDQUNqQ1Usc0RBQVFBLENBQUMsQ0FBQzBGO1FBQ1IsSUFBSUEsWUFBWXhFLElBQUksSUFBSTtZQUN0QitDLGVBQWU7WUFDZndCLGNBQWNDLGFBQWEsR0FBRztRQUNoQztJQUNGLEdBQUdwQyxvQkFDSDtRQUFDbUM7UUFBZW5DO1FBQW1CVztLQUFlO0lBR3BELE1BQU1vQyxXQUFXL0csa0RBQVdBLENBQUM7UUFDM0IsSUFBSSxDQUFDb0UsYUFBYVEsV0FBV0ksY0FBYztZQUN6Q21CLGNBQWNuQixjQUFjTixjQUFjLEdBQUc7UUFDL0M7SUFDRixHQUFHO1FBQUNOO1FBQVdRO1FBQVNJO1FBQWNOO1FBQWF5QjtLQUFjO0lBR2pFcEcsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN3RixvQkFBb0J5QixPQUFPLEVBQUU7UUFFbEMsTUFBTUMsV0FBVyxJQUFJQyxxQkFDbkIsQ0FBQ0M7WUFDQyxNQUFNLENBQUNDLE1BQU0sR0FBR0Q7WUFDaEIsSUFBSUMsTUFBTUMsY0FBYyxJQUFJekMsV0FBVyxDQUFDUixhQUFhckIsUUFBUVAsTUFBTSxHQUFHLEdBQUc7Z0JBQ3ZFdUU7WUFDRjtRQUNGLEdBQ0E7WUFDRU8sTUFBTS9CLG9CQUFvQnlCLE9BQU87WUFDakNPLFlBQVk7WUFDWkMsV0FBVztRQUNiO1FBR0YsTUFBTUMsV0FBV0MsU0FBU0MsY0FBYyxDQUFDO1FBQ3pDLElBQUlGLFVBQVU7WUFDWlIsU0FBU1csT0FBTyxDQUFDSDtRQUNuQjtRQUVBLE9BQU87WUFDTCxJQUFJQSxVQUFVO2dCQUNaUixTQUFTWSxTQUFTLENBQUNKO1lBQ3JCO1lBQ0FSLFNBQVNhLFVBQVU7UUFDckI7SUFDRixHQUFHO1FBQUNmO1FBQVVuQztRQUFTUjtRQUFXckIsUUFBUVAsTUFBTTtLQUFDO0lBRWpELDhCQUE4QjtJQUM5QnpDLGdEQUFTQSxDQUFDO1FBQ1IrRyxnQkFBZ0JpQixNQUFNO1FBQ3RCNUQsV0FBVyxFQUFFO1FBQ2JRLGVBQWU7UUFDZkUsV0FBVztRQUNYRSxjQUFjO1FBQ2RFLGdCQUFnQjtRQUVoQixJQUFJdEQsTUFBTUMsSUFBSSxJQUFJO1lBQ2hCeUMsYUFBYTtZQUNieUMsZ0JBQWdCbkY7UUFDbEIsT0FBTztZQUNMMEMsYUFBYTtRQUNmO1FBRUEsT0FBTyxJQUFNeUMsZ0JBQWdCaUIsTUFBTTtJQUNyQyxHQUFHO1FBQUNwRztRQUFPbUY7S0FBZ0I7SUFFM0IsTUFBTWtCLG9CQUFvQixDQUFDekU7UUFDekI2QixPQUFPaEQsSUFBSSxDQUFDOEMsSUFBSTNCLE9BQU8wRSxJQUFJO1FBQUlsRTtRQUFXbUUsaUJBQWlCdkc7SUFDN0Q7SUFFQSxNQUFNdUcsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUlBLE9BQU92RyxJQUFJLElBQUk7WUFDakIsTUFBTXdHLGtCQUFrQjtnQkFBQ0Q7bUJBQVczRCxlQUFlNkQsTUFBTSxDQUFDLENBQUNDLElBQU1BLE1BQU1IO2FBQVEsQ0FBQ0ksS0FBSyxDQUFDLEdBQUc7WUFDekY5RCxrQkFBa0IyRDtZQUNsQjNDLGFBQWErQyxPQUFPLENBQUMsa0JBQWtCN0MsS0FBSzhDLFNBQVMsQ0FBQ0w7UUFDeEQ7SUFDRjtJQUVBLE1BQU1NLHFCQUFxQixDQUFDUCxRQUFnQlE7UUFDMUNBLEVBQUVDLGVBQWU7UUFDakIsTUFBTVIsa0JBQWtCNUQsZUFBZTZELE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTUg7UUFDekQxRCxrQkFBa0IyRDtRQUNsQjNDLGFBQWErQyxPQUFPLENBQUMsa0JBQWtCN0MsS0FBSzhDLFNBQVMsQ0FBQ0w7SUFDeEQ7SUFFQyxNQUFNUyxtQkFBbUIsQ0FBQ3RGO1lBQ3JCQSxjQUNBQSxlQUlBQSxnQkFDQUEsaUJBQ0FBLGlCQUNBQTtRQVJKLEtBQUlBLGVBQUFBLE9BQU8wRSxJQUFJLGNBQVgxRSxtQ0FBQUEsYUFBYXVGLFFBQVEsQ0FBQyxtQkFBbUIscUJBQU8sOERBQUM3SCxnRkFBY0E7WUFBQ3NCLFdBQVU7Ozs7OztRQUM5RSxLQUFJZ0IsZ0JBQUFBLE9BQU8wRSxJQUFJLGNBQVgxRSxvQ0FBQUEsY0FBYXVGLFFBQVEsQ0FBQyxlQUFlLHFCQUFPLDhEQUFDNUgsMEVBQVFBO1lBQUNxQixXQUFVOzs7Ozs7UUFFcEUsSUFBSWdCLE9BQU93RixRQUFRLEVBQUUscUJBQU8sOERBQUN6SCxzREFBUUE7WUFBQzBILE1BQU16RixPQUFPd0YsUUFBUTtZQUFFeEcsV0FBVTs7Ozs7O1FBRXZFLElBQUlnQixFQUFBQSxpQkFBQUEsT0FBT0UsTUFBTSxjQUFiRixxQ0FBQUEsZUFBZUcsVUFBVSxLQUFJSCxPQUFPRSxNQUFNLENBQUN3RixRQUFRLEVBQUUscUJBQU8sOERBQUM5SCxnRkFBY0E7WUFBQ29CLFdBQVU7Ozs7OztRQUMxRixLQUFJZ0Isa0JBQUFBLE9BQU9FLE1BQU0sY0FBYkYsc0NBQUFBLGdCQUFlRyxVQUFVLEVBQUUscUJBQU8sOERBQUMzQywyRUFBU0E7WUFBQ3dCLFdBQVU7Ozs7OztRQUMzRCxLQUFJZ0Isa0JBQUFBLE9BQU9FLE1BQU0sY0FBYkYsc0NBQUFBLGdCQUFlSyxVQUFVLEVBQUUscUJBQU8sOERBQUM5QywwRUFBUUE7WUFBQ3lCLFdBQVU7Ozs7OztRQUMxRCxLQUFJZ0Isa0JBQUFBLE9BQU9FLE1BQU0sY0FBYkYsc0NBQUFBLGdCQUFlTSxNQUFNLEVBQUUscUJBQU8sOERBQUMvQywwRUFBUUE7WUFBQ3lCLFdBQVU7Ozs7OztRQUV0RCxJQUFJZ0IsT0FBT0MsS0FBSyxLQUFLMEYsYUFBYSxDQUFDM0YsT0FBT0UsTUFBTSxFQUFFLHFCQUFPLDhEQUFDekMsK0VBQWFBO1lBQUN1QixXQUFVOzs7Ozs7UUFDbEYscUJBQU8sOERBQUN6QiwwRUFBUUE7WUFBQ3lCLFdBQVU7Ozs7OztJQUM3QjtJQUVBLE1BQU00RyxnQkFBZ0IsQ0FBQzVGO1lBQ2pCQSxjQUNBQTtRQURKLEtBQUlBLGVBQUFBLE9BQU8wRSxJQUFJLGNBQVgxRSxtQ0FBQUEsYUFBYXVGLFFBQVEsQ0FBQyxtQkFBbUIsT0FBTztRQUNwRCxLQUFJdkYsZ0JBQUFBLE9BQU8wRSxJQUFJLGNBQVgxRSxvQ0FBQUEsY0FBYXVGLFFBQVEsQ0FBQyxlQUFlLE9BQU87UUFDaEQsSUFBSXZGLE9BQU9DLEtBQUssRUFBRSxPQUFPO1FBQ3pCLElBQUksQ0FBQ0QsT0FBT0UsTUFBTSxFQUFFLE9BQU87UUFFM0IsSUFBSUYsT0FBT0UsTUFBTSxDQUFDQyxVQUFVLElBQUlILE9BQU93RixRQUFRLEVBQUU7WUFDL0MsT0FBUXhGLE9BQU93RixRQUFRO2dCQUNyQixLQUFLeEgsMkVBQVFBLENBQUM2SCxLQUFLO29CQUFFLE9BQU87Z0JBQzVCLEtBQUs3SCwyRUFBUUEsQ0FBQzhILEtBQUs7b0JBQUUsT0FBTztnQkFDNUIsS0FBSzlILDJFQUFRQSxDQUFDK0gsU0FBUztvQkFBRSxPQUFPO2dCQUNoQyxLQUFLL0gsMkVBQVFBLENBQUNnSSxRQUFRO29CQUFFLE9BQU87Z0JBQy9CLEtBQUtoSSwyRUFBUUEsQ0FBQ2lJLElBQUk7b0JBQUUsT0FBTztnQkFDM0IsS0FBS2pJLDJFQUFRQSxDQUFDa0ksWUFBWTtvQkFBRSxPQUFPO2dCQUNuQyxLQUFLbEksMkVBQVFBLENBQUNtSSxRQUFRO29CQUFFLE9BQU87Z0JBQy9CO29CQUFTLE9BQU87WUFDbEI7UUFDRjtRQUVBLElBQUluRyxPQUFPRSxNQUFNLENBQUNDLFVBQVUsSUFBSUgsT0FBT0UsTUFBTSxDQUFDd0YsUUFBUSxFQUFFLE9BQU87UUFDL0QsSUFBSTFGLE9BQU9FLE1BQU0sQ0FBQ0MsVUFBVSxFQUFFLE9BQU87UUFDckMsSUFBSUgsT0FBT0UsTUFBTSxDQUFDRyxVQUFVLEVBQUUsT0FBTztRQUNyQyxJQUFJTCxPQUFPRSxNQUFNLENBQUNJLE1BQU0sRUFBRSxPQUFPO1FBRWpDLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDM0QseURBQU1BO1FBQUN5SixJQUFJO1FBQUNDLGNBQWM3RjtrQkFDekIsNEVBQUM1RCxnRUFBYUE7WUFBQ29DLFdBQVU7WUFBaUhzSCxZQUFZOzs4QkFDcEosOERBQUNDO29CQUFJdkgsV0FBVTs7c0NBQ2IsOERBQUM1QixxRkFBbUJBOzRCQUFDNEIsV0FBVTs7Ozs7O3NDQUMvQiw4REFBQ25DLHVEQUFLQTs0QkFBQ21DLFdBQVU7NEJBQ2Z3SCxhQUFhLFVBQW9ELE9BQTFDOUYsQ0FBQUEsc0JBQUFBLGlDQUFBQSx3QkFBQUEsVUFBV0EsU0FBUyxjQUFwQkEsNENBQUFBLHNCQUFzQitGLElBQUksS0FBSSxhQUFZOzRCQUNqRUMsT0FBT3RJOzRCQUFPdUksVUFBVSxDQUFDdkIsSUFBTXpFLFNBQVN5RSxFQUFFd0IsTUFBTSxDQUFDRixLQUFLOzRCQUFHRyxTQUFTOzs7Ozs7Ozs7Ozs7OEJBR3RFLDhEQUFDTjtvQkFBSXZILFdBQVU7b0JBQW9EOEgsS0FBSzlFOzt3QkFDckVuQiwyQkFDQyw4REFBQzBGOzRCQUFJdkgsV0FBVTtzQ0FDYiw0RUFBQ3VIO2dDQUFJdkgsV0FBVTtnQ0FBeUMrSCxPQUFPO29DQUFFQyxPQUFPO2dDQUFPOzs7Ozs7Ozs7Ozt3QkFHbEZuRyxhQUFhTSxnQkFBZ0IsS0FBSzNCLFFBQVFQLE1BQU0sS0FBSyxrQkFDcEQsOERBQUNzSDs0QkFBSXZILFdBQVU7OzhDQUNiLDhEQUFDbEIsaUVBQU1BO29DQUFDa0IsV0FBVTs7Ozs7OzhDQUNsQiw4REFBQ2lJO29DQUFFakksV0FBVTs4Q0FBcUM7Ozs7Ozs7Ozs7O21DQUVsRCtCLHNCQUNGLDhEQUFDd0Y7NEJBQUl2SCxXQUFVOzs4Q0FDYiw4REFBQ3VIO29DQUFJdkgsV0FBVTs4Q0FDYiw0RUFBQ25CLCtFQUFhQTt3Q0FBQ21CLFdBQVU7Ozs7Ozs7Ozs7OzhDQUUzQiw4REFBQ2tJO29DQUFHbEksV0FBVTs4Q0FBb0M7Ozs7Ozs4Q0FDbEQsOERBQUNpSTtvQ0FBRWpJLFdBQVU7OENBQTZCOzs7Ozs7Ozs7OzttQ0FFMUMsQ0FBQ1osTUFBTUMsSUFBSSxtQkFDYiw4REFBQ2tJOzRCQUFJdkgsV0FBVTs7OENBQ2IsOERBQUNrSTtvQ0FBR2xJLFdBQVU7OENBQXVGOzs7Ozs7Z0NBQ3BHaUMsZUFBZWhDLE1BQU0sR0FBRyxJQUN2QmdDLGVBQWVrRyxHQUFHLENBQUMsQ0FBQ3ZDLFFBQVF3QyxzQkFDMUIsOERBQUNiO3dDQUFnQnZILFdBQVU7d0NBQ3pCcUksU0FBUyxJQUFNMUcsU0FBU2lFOzswREFDeEIsOERBQUMyQjtnREFBSXZILFdBQVU7O2tFQUNiLDhEQUFDM0IsMkVBQVNBO3dEQUFDMkIsV0FBVTs7Ozs7O2tFQUNyQiw4REFBQ3NJO3dEQUFLdEksV0FBVTtrRUFBeUI0Rjs7Ozs7Ozs7Ozs7OzBEQUUzQyw4REFBQzJDO2dEQUFPRixTQUFTLENBQUNqQyxJQUFNRCxtQkFBbUJQLFFBQVFRO2dEQUFJcEcsV0FBVTswREFDL0QsNEVBQUMxQiwyRUFBU0E7b0RBQUMwQixXQUFVOzs7Ozs7Ozs7Ozs7dUNBUGZvSTs7Ozs4REFXViw4REFBQ2I7b0NBQUl2SCxXQUFVOzhDQUFrQzs7Ozs7Ozs7Ozs7bUNBRXJEUSxRQUFRUCxNQUFNLEtBQUssa0JBQ3JCLDhEQUFDc0g7NEJBQUl2SCxXQUFVOzs4Q0FDYiw4REFBQ3VIO29DQUFJdkgsV0FBVTs4Q0FDYiw0RUFBQzVCLHFGQUFtQkE7d0NBQUM0QixXQUFVOzs7Ozs7Ozs7Ozs4Q0FFakMsOERBQUNrSTtvQ0FBR2xJLFdBQVU7OENBQW9DOzs7Ozs7OENBQ2xELDhEQUFDaUk7b0NBQUVqSSxXQUFVOzt3Q0FBNkI7d0NBQXFDWjt3Q0FBTTs7Ozs7Ozs7Ozs7O21DQUd2Rm9KLE9BQU81RCxPQUFPLENBQUNyRSxhQUFhQyxVQUFVMkgsR0FBRyxDQUFDO2dDQUFDLENBQUNNLFVBQVVDLE1BQU07bUNBQzFEQSxNQUFNekksTUFBTSxHQUFHLG1CQUNiLDhEQUFDc0g7O2tEQUNDLDhEQUFDVzt3Q0FBR2xJLFdBQVU7a0RBQXdHeUk7Ozs7OztvQ0FDckhDLE1BQU1QLEdBQUcsQ0FBQyxDQUFDbkgsdUJBQ1YsOERBQUN1Rzs0Q0FBb0J2SCxXQUFVOzRDQUM3QnFJLFNBQVMsSUFBTTVDLGtCQUFrQnpFOztnREFDaENBLE9BQU9DLEtBQUssaUJBQ1gsOERBQUMwSDtvREFBSUMsS0FBSzVILE9BQU9DLEtBQUs7b0RBQUU0SCxLQUFLN0gsT0FBT3lHLElBQUksSUFBSTtvREFBSXpILFdBQVU7Ozs7O3lFQUUxRCw4REFBQ3VIO29EQUFJdkgsV0FBVTs4REFDWnNHLGlCQUFpQnRGOzs7Ozs7OERBR3RCLDhEQUFDdUc7b0RBQUl2SCxXQUFVOztzRUFDYiw4REFBQ3VIOzREQUFJdkgsV0FBVTs7OEVBQ2IsOERBQUNrSTtvRUFBR2xJLFdBQVU7OEVBQ1osNEVBQUNmO3dFQUNDQyxTQUFTOEIsT0FBTzhILEtBQUssSUFBSTlILE9BQU95RyxJQUFJO3dFQUNwQ3RJLFdBQVc2QixPQUFPN0IsU0FBUzt3RUFDM0JDLE9BQU9BOzs7Ozs7Ozs7OztnRUFHVjRCLE9BQU8rSCxXQUFXLGtCQUNqQiw4REFBQ1Q7b0VBQUt0SSxXQUFVOzhFQUNiOUIsZ0dBQW1CQSxDQUFDLElBQUk4SyxLQUFLaEksT0FBTytILFdBQVcsR0FBRzt3RUFBRUUsV0FBVztvRUFBSzs7Ozs7Ozs7Ozs7O3dEQUkxRWpJLE9BQU85QixPQUFPLGtCQUNiLDhEQUFDK0k7NERBQUVqSSxXQUFVO3NFQUNYLDRFQUFDZjtnRUFDQ0MsU0FBUzhCLE9BQU85QixPQUFPO2dFQUN2QkMsV0FBVzZCLE9BQU83QixTQUFTO2dFQUMzQkMsT0FBT0E7Ozs7Ozs7Ozs7O3dEQUlaNEIsT0FBT0UsTUFBTSxrQkFDWiw4REFBQ3FHOzREQUFJdkgsV0FBVTtzRUFDYiw0RUFBQ3NJO2dFQUFLdEksV0FBVTswRUFDYjRHLGNBQWM1Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQXBDZkEsT0FBTytCLEVBQUU7Ozs7OzsrQkFIYjBGOzs7Ozs7d0JBbURmNUcsYUFBYU0sY0FBYyxtQkFDMUIsOERBQUNvRjs0QkFBSXZILFdBQVU7c0NBQ2IsNEVBQUNsQixpRUFBTUE7Z0NBQUNrQixXQUFVOzs7Ozs7Ozs7OztzQ0FLdEIsOERBQUN1SDs0QkFBSXhFLElBQUc7NEJBQTBCL0MsV0FBVTs7Ozs7O3dCQUUzQyxDQUFDNkIsYUFBYXJCLFFBQVFQLE1BQU0sR0FBRyxtQkFDOUIsOERBQUNzSDs0QkFBSXZILFdBQVU7c0NBQ1pxQyx3QkFDQyw4REFBQ2tGO2dDQUFJdkgsV0FBVTswQ0FBd0I7Ozs7O3FEQUl2Qyw4REFBQ3VIO2dDQUFJdkgsV0FBVTswQ0FDWnVDLGFBQWEsSUFBSSxlQUEwQixPQUFYQSxZQUFXLGNBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTMUU7R0F4VXdCaEI7O1FBTUt6RCw4REFBWUE7UUFBZ0JDLG9EQUFPQTtRQUFhRSxzREFBU0E7OztNQU45RHNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi9zZWFyY2htb2RhbC50c3g/OGJkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ0NvbnRlbnQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgdXNlV29ya3NwYWNlIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3dvcmtzcGFjZVwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3VzZXJcIlxuaW1wb3J0IHsgc2VhcmNoV29ya3NwYWNlcyB9IGZyb20gXCJAL2FwaS93b3Jrc3BhY2VcIlxuaW1wb3J0IHsgU2VhcmNoUmVzdWx0IH0gZnJvbSBcIkAvdHlwaW5ncy93b3Jrc3BhY2VcIlxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXG5pbXBvcnQgeyBmb3JtYXREaXN0YW5jZVRvTm93IH0gZnJvbSBcImRhdGUtZm5zXCJcbmltcG9ydCBkZWJvdW5jZSBmcm9tIFwibG9kYXNoL2RlYm91bmNlXCJcbmltcG9ydCB7TWFnbmlmeWluZ0dsYXNzSWNvbiwgVGltZXJJY29uLCBYbWFya0ljb24sIEJvb2tJY29uLCBUYWJsZUljb24sIFVzZXJHcm91cEljb24sIENsb2NrVGhyZWVJY29uLCBOb3RlSWNvbiwgU3F1YXJlTGlzdEljb24sIFdpZmlTbGFzaEljb259IGZyb20gXCJAL2NvbXBvbmVudHMvaWNvbnMvRm9udEF3ZXNvbWVSZWd1bGFyXCJcbmltcG9ydCB7IExvYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvY3VzdG9tLXVpL2xvYWRlclwiXG5pbXBvcnQgeyBWaWV3SWNvbiB9IGZyb20gXCIuLi92aWV3cy92aWV3SWNvblwiXG5pbXBvcnQgeyBWaWV3VHlwZSB9IGZyb20gXCJvcGVuZGItYXBwLWRiLXV0aWxzL2xpYi90eXBpbmdzL3ZpZXdcIlxuXG5pbnRlcmZhY2UgU2VhcmNoTW9kYWxQcm9wcyB7IG9uQ2xvc2U6ICgpID0+IHZvaWQ7IGRlYm91bmNlVGltZW91dE1TPzogbnVtYmVyIH1cblxuY29uc3QgSGlnaGxpZ2h0ZWRDb250ZW50ID0gKHsgY29udGVudCwgaGlnaGxpZ2h0LCBxdWVyeSB9OlxuICB7IGNvbnRlbnQ/OiBzdHJpbmc7IGhpZ2hsaWdodD86IHsgc3RhcnQ6IG51bWJlcjsgZW5kOiBudW1iZXIgfTsgcXVlcnk/OiBzdHJpbmcgfSkgPT4ge1xuICBpZiAoIWNvbnRlbnQpIHJldHVybiBudWxsO1xuXG4gIGlmIChxdWVyeSAmJiBxdWVyeS50cmltKCkpIHtcbiAgICBjb25zdCBzZWFyY2hUZXJtID0gcXVlcnkudHJpbSgpLnRvTG93ZXJDYXNlKCk7XG4gICAgY29uc3QgY29udGVudExvd2VyID0gY29udGVudC50b0xvd2VyQ2FzZSgpO1xuICAgIGNvbnN0IHBhcnRzID0gW107XG4gICAgbGV0IGxhc3RJbmRleCA9IDA7XG4gICAgbGV0IGN1cnJlbnRJbmRleCA9IGNvbnRlbnRMb3dlci5pbmRleE9mKHNlYXJjaFRlcm0sIGxhc3RJbmRleCk7XG5cbiAgICBpZiAoY3VycmVudEluZGV4ID09PSAtMSkgcmV0dXJuIDw+e2NvbnRlbnR9PC8+O1xuICAgIHdoaWxlIChjdXJyZW50SW5kZXggIT09IC0xKSB7XG4gICAgICBwYXJ0cy5wdXNoKGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCwgY3VycmVudEluZGV4KSk7XG4gICAgICBwYXJ0cy5wdXNoKFxuICAgICAgICA8bWFyayBrZXk9e2N1cnJlbnRJbmRleH0gY2xhc3NOYW1lPVwiYmcteWVsbG93LTIwMFwiPlxuICAgICAgICAgIHtjb250ZW50LnN1YnN0cmluZyhjdXJyZW50SW5kZXgsIGN1cnJlbnRJbmRleCArIHNlYXJjaFRlcm0ubGVuZ3RoKX1cbiAgICAgICAgPC9tYXJrPlxuICAgICAgKTtcbiAgICAgIGxhc3RJbmRleCA9IGN1cnJlbnRJbmRleCArIHNlYXJjaFRlcm0ubGVuZ3RoO1xuICAgICAgY3VycmVudEluZGV4ID0gY29udGVudExvd2VyLmluZGV4T2Yoc2VhcmNoVGVybSwgbGFzdEluZGV4KTtcbiAgICB9XG5cbiAgICBpZiAobGFzdEluZGV4IDwgY29udGVudC5sZW5ndGgpIHtcbiAgICAgIHBhcnRzLnB1c2goY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4KSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIDw+e3BhcnRzfTwvPjtcbiAgfVxuXG4gIGlmIChoaWdobGlnaHQgJiYgaGlnaGxpZ2h0LnN0YXJ0ID49IDApIHtcbiAgICBjb25zdCBzdGFydCA9IE1hdGgubWF4KDAsIGhpZ2hsaWdodC5zdGFydCk7XG4gICAgY29uc3QgZW5kID0gTWF0aC5taW4oY29udGVudC5sZW5ndGgsIGhpZ2hsaWdodC5lbmQpO1xuXG5cbiAgICBpZiAoc3RhcnQgPCBlbmQgJiYgc3RhcnQgPCBjb250ZW50Lmxlbmd0aCkge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPD57Y29udGVudC5zdWJzdHJpbmcoMCwgc3RhcnQpfVxuICAgICAgICAgIDxtYXJrIGNsYXNzTmFtZT1cImJnLXllbGxvdy0yMDBcIj57Y29udGVudC5zdWJzdHJpbmcoc3RhcnQsIGVuZCl9PC9tYXJrPlxuICAgICAgICAgIHtjb250ZW50LnN1YnN0cmluZyhlbmQpfVxuICAgICAgICA8Lz5cbiAgICAgICk7XG4gICAgfVxuICB9XG4gIHJldHVybiA8Pntjb250ZW50fTwvPjtcbn07XG5cbmludGVyZmFjZSBHcm91cGVkUmVzdWx0cyB7XG4gIGRhdGFiYXNlczogU2VhcmNoUmVzdWx0W107IHBhZ2VzOiBTZWFyY2hSZXN1bHRbXTsgdmlld3M6IFNlYXJjaFJlc3VsdFtdO1xuICBkb2N1bWVudHM6IFNlYXJjaFJlc3VsdFtdOyBtZW1iZXJzOiBTZWFyY2hSZXN1bHRbXTtcbn1cblxuY29uc3QgZ3JvdXBSZXN1bHRzID0gKHJlc3VsdHM6IFNlYXJjaFJlc3VsdFtdKTogR3JvdXBlZFJlc3VsdHMgPT4ge1xuICBjb25zdCBncm91cGVkID0geyBkYXRhYmFzZXM6IFtdLCBwYWdlczogW10sIHZpZXdzOiBbXSwgZG9jdW1lbnRzOiBbXSwgbWVtYmVyczogW10gfSBhcyBHcm91cGVkUmVzdWx0cztcbiAgcmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB7XG4gICAgaWYgKHJlc3VsdC5pbWFnZSB8fCAhcmVzdWx0LnNvdXJjZSkge1xuICAgICAgZ3JvdXBlZC5tZW1iZXJzLnB1c2gocmVzdWx0KTtcbiAgICB9IGVsc2UgaWYgKHJlc3VsdC5zb3VyY2UpIHtcbiAgICAgIGlmIChyZXN1bHQuc291cmNlLmRhdGFiYXNlSWQgJiYgIXJlc3VsdC5zb3VyY2Uudmlld0lkKSBncm91cGVkLmRhdGFiYXNlcy5wdXNoKHJlc3VsdCk7XG4gICAgICBlbHNlIGlmIChyZXN1bHQuc291cmNlLnZpZXdJZCkgZ3JvdXBlZC52aWV3cy5wdXNoKHJlc3VsdCk7XG4gICAgICBlbHNlIGlmIChyZXN1bHQuc291cmNlLmRvY3VtZW50SWQpIGdyb3VwZWQuZG9jdW1lbnRzLnB1c2gocmVzdWx0KTtcbiAgICAgIGVsc2UgaWYgKHJlc3VsdC5zb3VyY2UucGFnZUlkKSBncm91cGVkLnBhZ2VzLnB1c2gocmVzdWx0KTtcbiAgICB9XG4gIH0pO1xuICByZXR1cm4gZ3JvdXBlZDtcbn07XG5cbmludGVyZmFjZSBBcGlFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgcmVzcG9uc2U/OiB7IGRhdGE/OiB7IGVycm9yPzogc3RyaW5nOyBtZXNzYWdlPzogc3RyaW5nOyBzdGF0dXM/OiBudW1iZXIgfSB9O1xuICBzdGF0dXM/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlYXJjaE1vZGFsKHsgb25DbG9zZSwgZGVib3VuY2VUaW1lb3V0TVMgPSAyNTAwIH06IFNlYXJjaE1vZGFsUHJvcHMpIHtcbiAgY29uc3QgW3F1ZXJ5LCBzZXRRdWVyeV0gPSB1c2VTdGF0ZShcIlwiKSwgW3Jlc3VsdHMsIHNldFJlc3VsdHNdID0gdXNlU3RhdGU8U2VhcmNoUmVzdWx0W10+KFtdKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpLCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtyZWNlbnRTZWFyY2hlcywgc2V0UmVjZW50U2VhcmNoZXNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKVxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpLCBbaGFzTW9yZSwgc2V0SGFzTW9yZV0gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbdG90YWxJdGVtcywgc2V0VG90YWxJdGVtc10gPSB1c2VTdGF0ZSgwKSwgW2N1cnJlbnRRdWVyeSwgc2V0Q3VycmVudFF1ZXJ5XSA9IHVzZVN0YXRlKFwiXCIpXG4gIGNvbnN0IHsgd29ya3NwYWNlLCB1cmwgfSA9IHVzZVdvcmtzcGFjZSgpLCB7IHRva2VuIH0gPSB1c2VBdXRoKCksIHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHdvcmtzcGFjZUlkID0gd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkXG4gIGNvbnN0IHJlc3VsdHNDb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQgfCBudWxsPihudWxsKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRTZWFyY2hlcyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwicmVjZW50U2VhcmNoZXNcIilcbiAgICBpZiAoc2F2ZWRTZWFyY2hlcykgc2V0UmVjZW50U2VhcmNoZXMoSlNPTi5wYXJzZShzYXZlZFNlYXJjaGVzKSlcbiAgfSwgW10pXG5cbiAgY29uc3QgZ2V0RXJyb3JNZXNzYWdlID0gKGVycm9yOiB1bmtub3duKTogc3RyaW5nID0+IHtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgaWYgKCdyZXNwb25zZScgaW4gZXJyb3IpIHtcbiAgICAgICAgY29uc3QgYXBpRXJyb3IgPSBlcnJvciBhcyBBcGlFcnJvcjtcbiAgICAgICAgcmV0dXJuIGFwaUVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGFwaUVycm9yLm1lc3NhZ2U7XG4gICAgICB9XG4gICAgICByZXR1cm4gZXJyb3IubWVzc2FnZTtcbiAgICB9XG4gICAgcmV0dXJuICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJztcbiAgfTtcblxuICBjb25zdCBwZXJmb3JtU2VhcmNoID0gdXNlQ2FsbGJhY2soYXN5bmMgKHNlYXJjaFF1ZXJ5OiBzdHJpbmcsIHBhZ2UgPSAxLCBhcHBlbmQgPSBmYWxzZSkgPT4ge1xuICAgIGlmICghc2VhcmNoUXVlcnkudHJpbSgpIHx8ICF3b3Jrc3BhY2VJZCB8fCAhdG9rZW4pIHtcbiAgICAgIHNldFJlc3VsdHMoW10pOyBzZXRFcnJvcihudWxsKTsgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldEhhc01vcmUoZmFsc2UpOyBzZXRUb3RhbEl0ZW1zKDApOyBzZXRDdXJyZW50UGFnZSgxKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50U2VhcmNoUXVlcnkgPSBzZWFyY2hRdWVyeTtcblxuICAgIGlmIChwYWdlID09PSAxKSBzZXRIYXNNb3JlKHRydWUpO1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNlYXJjaFdvcmtzcGFjZXModG9rZW4udG9rZW4sIHdvcmtzcGFjZUlkLCBjdXJyZW50U2VhcmNoUXVlcnksIHBhZ2UsIDI1KTtcbiAgICAgIGlmICghcmVzcG9uc2UuaXNTdWNjZXNzKSB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UuZXJyb3IgfHwgJ1NlYXJjaCBmYWlsZWQnKTtcblxuICAgICAgY29uc3QgbmV3UmVzdWx0cyA9IHJlc3BvbnNlLmRhdGEuZGF0YS5yZXN1bHRzLnJlc3VsdHMgfHwgW107XG4gICAgICBjb25zdCBwYWdpbmF0aW9uID0gcmVzcG9uc2UuZGF0YS5kYXRhLnJlc3VsdHMucGFnaW5hdGlvbjtcblxuICAgICAgaWYgKGN1cnJlbnRTZWFyY2hRdWVyeSA9PT0gcXVlcnkpIHtcbiAgICAgICAgc2V0VG90YWxJdGVtcyhwYWdpbmF0aW9uLnRvdGFsSXRlbXMpO1xuICAgICAgICBzZXRIYXNNb3JlKHBhZ2UgPCBwYWdpbmF0aW9uLnRvdGFsUGFnZXMpO1xuXG4gICAgICAgIHNldFJlc3VsdHMoYXBwZW5kID8gcHJldiA9PiBbLi4ucHJldiwgLi4ubmV3UmVzdWx0c10gOiBuZXdSZXN1bHRzKTtcbiAgICAgICAgc2V0Q3VycmVudFBhZ2UocGFnZSk7XG4gICAgICAgIHNldEN1cnJlbnRRdWVyeShjdXJyZW50U2VhcmNoUXVlcnkpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiU2VhcmNoIGVycm9yOlwiLCBlcnJvcik7XG4gICAgICBpZiAoIWFwcGVuZCAmJiBjdXJyZW50U2VhcmNoUXVlcnkgPT09IHF1ZXJ5KSB7XG4gICAgICAgIHNldEVycm9yKGdldEVycm9yTWVzc2FnZShlcnJvcikpO1xuICAgICAgfVxuICAgIH0gZmluYWxseSB7XG4gICAgICBpZiAoY3VycmVudFNlYXJjaFF1ZXJ5ID09PSBxdWVyeSkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3dvcmtzcGFjZUlkLCB0b2tlbiwgcXVlcnldKTtcblxuICBjb25zdCBkZWJvdW5jZWRTZWFyY2ggPSB1c2VDYWxsYmFjayhcbiAgICBkZWJvdW5jZSgoc2VhcmNoUXVlcnk6IHN0cmluZykgPT4ge1xuICAgICAgaWYgKHNlYXJjaFF1ZXJ5LnRyaW0oKSkge1xuICAgICAgICBzZXRDdXJyZW50UGFnZSgxKTtcbiAgICAgICAgcGVyZm9ybVNlYXJjaChzZWFyY2hRdWVyeSwgMSwgZmFsc2UpO1xuICAgICAgfVxuICAgIH0sIGRlYm91bmNlVGltZW91dE1TKSxcbiAgICBbcGVyZm9ybVNlYXJjaCwgZGVib3VuY2VUaW1lb3V0TVMsIHNldEN1cnJlbnRQYWdlXVxuICApO1xuXG4gIGNvbnN0IGxvYWRNb3JlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghaXNMb2FkaW5nICYmIGhhc01vcmUgJiYgY3VycmVudFF1ZXJ5KSB7XG4gICAgICBwZXJmb3JtU2VhcmNoKGN1cnJlbnRRdWVyeSwgY3VycmVudFBhZ2UgKyAxLCB0cnVlKTtcbiAgICB9XG4gIH0sIFtpc0xvYWRpbmcsIGhhc01vcmUsIGN1cnJlbnRRdWVyeSwgY3VycmVudFBhZ2UsIHBlcmZvcm1TZWFyY2hdKTtcblxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFyZXN1bHRzQ29udGFpbmVyUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKFxuICAgICAgKGVudHJpZXMpID0+IHtcbiAgICAgICAgY29uc3QgW2VudHJ5XSA9IGVudHJpZXM7XG4gICAgICAgIGlmIChlbnRyeS5pc0ludGVyc2VjdGluZyAmJiBoYXNNb3JlICYmICFpc0xvYWRpbmcgJiYgcmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgbG9hZE1vcmUoKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcm9vdDogcmVzdWx0c0NvbnRhaW5lclJlZi5jdXJyZW50LFxuICAgICAgICByb290TWFyZ2luOiAnMHB4IDBweCAyMDBweCAwcHgnLFxuICAgICAgICB0aHJlc2hvbGQ6IDAuMVxuICAgICAgfVxuICAgICk7XG5cbiAgICBjb25zdCBzZW50aW5lbCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzZWFyY2gtcmVzdWx0cy1zZW50aW5lbCcpO1xuICAgIGlmIChzZW50aW5lbCkge1xuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZShzZW50aW5lbCk7XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChzZW50aW5lbCkge1xuICAgICAgICBvYnNlcnZlci51bm9ic2VydmUoc2VudGluZWwpO1xuICAgICAgfVxuICAgICAgb2JzZXJ2ZXIuZGlzY29ubmVjdCgpO1xuICAgIH07XG4gIH0sIFtsb2FkTW9yZSwgaGFzTW9yZSwgaXNMb2FkaW5nLCByZXN1bHRzLmxlbmd0aF0pO1xuXG4gIC8vIEhhbmRsZSBzZWFyY2ggaW5wdXQgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGRlYm91bmNlZFNlYXJjaC5jYW5jZWwoKTtcbiAgICBzZXRSZXN1bHRzKFtdKTtcbiAgICBzZXRDdXJyZW50UGFnZSgxKTtcbiAgICBzZXRIYXNNb3JlKGZhbHNlKTtcbiAgICBzZXRUb3RhbEl0ZW1zKDApO1xuICAgIHNldEN1cnJlbnRRdWVyeShcIlwiKTtcblxuICAgIGlmIChxdWVyeS50cmltKCkpIHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgIGRlYm91bmNlZFNlYXJjaChxdWVyeSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IGRlYm91bmNlZFNlYXJjaC5jYW5jZWwoKTtcbiAgfSwgW3F1ZXJ5LCBkZWJvdW5jZWRTZWFyY2hdKVxuXG4gIGNvbnN0IGhhbmRsZVJlc3VsdENsaWNrID0gKHJlc3VsdDogU2VhcmNoUmVzdWx0KSA9PiB7XG4gICAgcm91dGVyLnB1c2godXJsKHJlc3VsdC5wYXRoKSk7IG9uQ2xvc2UoKTsgc2F2ZVJlY2VudFNlYXJjaChxdWVyeSk7XG4gIH1cblxuICBjb25zdCBzYXZlUmVjZW50U2VhcmNoID0gKHNlYXJjaDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHNlYXJjaC50cmltKCkpIHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRTZWFyY2hlcyA9IFtzZWFyY2gsIC4uLnJlY2VudFNlYXJjaGVzLmZpbHRlcigocykgPT4gcyAhPT0gc2VhcmNoKV0uc2xpY2UoMCwgNSlcbiAgICAgIHNldFJlY2VudFNlYXJjaGVzKHVwZGF0ZWRTZWFyY2hlcylcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwicmVjZW50U2VhcmNoZXNcIiwgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFNlYXJjaGVzKSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBkZWxldGVSZWNlbnRTZWFyY2ggPSAoc2VhcmNoOiBzdHJpbmcsIGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgY29uc3QgdXBkYXRlZFNlYXJjaGVzID0gcmVjZW50U2VhcmNoZXMuZmlsdGVyKHMgPT4gcyAhPT0gc2VhcmNoKVxuICAgIHNldFJlY2VudFNlYXJjaGVzKHVwZGF0ZWRTZWFyY2hlcylcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInJlY2VudFNlYXJjaGVzXCIsIEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRTZWFyY2hlcykpXG4gIH1cblxuICAgY29uc3QgZ2V0SWNvbkZvclNvdXJjZSA9IChyZXN1bHQ6IFNlYXJjaFJlc3VsdCkgPT4ge1xuICAgIGlmIChyZXN1bHQucGF0aD8uaW5jbHVkZXMoJz90YWI9cmVtaW5kZXJzJykpIHJldHVybiA8Q2xvY2tUaHJlZUljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPjtcbiAgICBpZiAocmVzdWx0LnBhdGg/LmluY2x1ZGVzKCc/dGFiPW5vdGVzJykpIHJldHVybiA8Tm90ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIi8+O1xuXG4gICAgaWYgKHJlc3VsdC52aWV3VHlwZSkgcmV0dXJuIDxWaWV3SWNvbiB0eXBlPXtyZXN1bHQudmlld1R5cGV9IGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz47XG5cbiAgICBpZiAocmVzdWx0LnNvdXJjZT8uZGF0YWJhc2VJZCAmJiByZXN1bHQuc291cmNlLnJlY29yZElkKSByZXR1cm4gPFNxdWFyZUxpc3RJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wcmltYXJ5XCIgLz47XG4gICAgaWYgKHJlc3VsdC5zb3VyY2U/LmRhdGFiYXNlSWQpIHJldHVybiA8VGFibGVJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wcmltYXJ5XCIgLz47XG4gICAgaWYgKHJlc3VsdC5zb3VyY2U/LmRvY3VtZW50SWQpIHJldHVybiA8Qm9va0ljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIgLz47XG4gICAgaWYgKHJlc3VsdC5zb3VyY2U/LnBhZ2VJZCkgcmV0dXJuIDxCb29rSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmRcIiAvPjtcblxuICAgIGlmIChyZXN1bHQuaW1hZ2UgIT09IHVuZGVmaW5lZCB8fCAhcmVzdWx0LnNvdXJjZSkgcmV0dXJuIDxVc2VyR3JvdXBJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz47XG4gICAgcmV0dXJuIDxCb29rSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+O1xuICB9O1xuXG4gIGNvbnN0IGdldFJlc3VsdFR5cGUgPSAocmVzdWx0OiBTZWFyY2hSZXN1bHQpID0+IHtcbiAgICBpZiAocmVzdWx0LnBhdGg/LmluY2x1ZGVzKCc/dGFiPXJlbWluZGVycycpKSByZXR1cm4gXCJSZW1pbmRlclwiO1xuICAgIGlmIChyZXN1bHQucGF0aD8uaW5jbHVkZXMoJz90YWI9bm90ZXMnKSkgcmV0dXJuIFwiTm90ZVwiO1xuICAgIGlmIChyZXN1bHQuaW1hZ2UpIHJldHVybiBcIk1lbWJlclwiO1xuICAgIGlmICghcmVzdWx0LnNvdXJjZSkgcmV0dXJuIFwiTWVtYmVyXCI7XG5cbiAgICBpZiAocmVzdWx0LnNvdXJjZS5kYXRhYmFzZUlkICYmIHJlc3VsdC52aWV3VHlwZSkge1xuICAgICAgc3dpdGNoIChyZXN1bHQudmlld1R5cGUpIHtcbiAgICAgICAgY2FzZSBWaWV3VHlwZS5UYWJsZTogcmV0dXJuIFwiVGFibGUgVmlld1wiO1xuICAgICAgICBjYXNlIFZpZXdUeXBlLkJvYXJkOiByZXR1cm4gXCJCb2FyZCBWaWV3XCI7XG4gICAgICAgIGNhc2UgVmlld1R5cGUuRGFzaGJvYXJkOiByZXR1cm4gXCJEYXNoYm9hcmRcIjtcbiAgICAgICAgY2FzZSBWaWV3VHlwZS5Eb2N1bWVudDogcmV0dXJuIFwiRG9jdW1lbnQgVmlld1wiO1xuICAgICAgICBjYXNlIFZpZXdUeXBlLkZvcm06IHJldHVybiBcIkZvcm0gVmlld1wiO1xuICAgICAgICBjYXNlIFZpZXdUeXBlLlN1bW1hcnlUYWJsZTogcmV0dXJuIFwiU3VtbWFyeSBUYWJsZVwiO1xuICAgICAgICBjYXNlIFZpZXdUeXBlLkxpc3RWaWV3OiByZXR1cm4gXCJMaXN0IFZpZXdcIjtcbiAgICAgICAgZGVmYXVsdDogcmV0dXJuIFwiRGF0YWJhc2UgVmlld1wiO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChyZXN1bHQuc291cmNlLmRhdGFiYXNlSWQgJiYgcmVzdWx0LnNvdXJjZS5yZWNvcmRJZCkgcmV0dXJuIFwiUmVjb3JkXCI7XG4gICAgaWYgKHJlc3VsdC5zb3VyY2UuZGF0YWJhc2VJZCkgcmV0dXJuIFwiRGF0YWJhc2VcIjtcbiAgICBpZiAocmVzdWx0LnNvdXJjZS5kb2N1bWVudElkKSByZXR1cm4gXCJEb2N1bWVudFwiO1xuICAgIGlmIChyZXN1bHQuc291cmNlLnBhZ2VJZCkgcmV0dXJuIFwiUGFnZVwiO1xuXG4gICAgcmV0dXJuIFwiRG9jdW1lbnRcIjtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3BlbiBvbk9wZW5DaGFuZ2U9e29uQ2xvc2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwicC0wIGdhcC0wIHctWzk1dnddIG1heC13LXhsIHJvdW5kZWQtbGcgc2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgb3ZlcmZsb3ctaGlkZGVuIHNtOnctZnVsbCBtYXgtaC1bOTB2aF1cIiBoaWRlQ2xvc2VCdG4+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgYm9yZGVyLWIgcHgtMiBzbTpweC0zIHJlbGF0aXZlIGJnLWdyYXktNTBcIj5cbiAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgc2hyaW5rLTAgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgPElucHV0IGNsYXNzTmFtZT1cImZsZXggaC0xMCBzbTpoLTEyIHJvdW5kZWQtbWQgYm9yZGVyLTAgYmctdHJhbnNwYXJlbnQgcHktMS41IHNtOnB5LTIgdGV4dC14cyBvdXRsaW5lLW5vbmUgcGxhY2Vob2xkZXI6dGV4dC1ncmF5LTUwMCBmb2N1cy12aXNpYmxlOnJpbmctMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj17YFNlYXJjaCAke3dvcmtzcGFjZT8ud29ya3NwYWNlPy5uYW1lIHx8ICd3b3Jrc3BhY2UnfS4uLmB9XG4gICAgICAgICAgICB2YWx1ZT17cXVlcnl9IG9uQ2hhbmdlPXsoZSkgPT4gc2V0UXVlcnkoZS50YXJnZXQudmFsdWUpfSBhdXRvRm9jdXMgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC1bNjB2aF0gc206bWF4LWgtWzY1dmhdIG92ZXJmbG93LXktYXV0byBwci0xXCIgcmVmPXtyZXN1bHRzQ29udGFpbmVyUmVmfT5cbiAgICAgICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTAgbGVmdC0wIHJpZ2h0LTAgaC0wLjUgei01MFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXByaW1hcnktNTAwLzMwIGgtZnVsbCBhbmltYXRlLXB1bHNlXCIgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fT48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge2lzTG9hZGluZyAmJiBjdXJyZW50UGFnZSA9PT0gMSAmJiByZXN1bHRzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBzbTpweC02IHB5LTYgc206cHktOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TG9hZGVyIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayB3LTUgaC01IHNtOnctNiBzbTpoLTYgdGV4dC1ncmF5LTYwMCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHNtOm10LTMgdGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+U2VhcmNoaW5nIHdvcmtzcGFjZS4uLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiBlcnJvciA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBzbTpweC02IHB5LTYgc206cHktOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gZmxleCBoLTEwIHctMTAgc206aC0xMiBzbTp3LTEyIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgYmctZ3JheS0xMDAgbWItMiBzbTptYi0zXCI+XG4gICAgICAgICAgICAgICAgPFdpZmlTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBzbTpoLTUgc206dy01IHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPk5ldHdvcmsgRXJyb3I8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlBsZWFzZSBjaGVjayB5b3VyIGludGVybmV0IGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2FpbjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAhcXVlcnkudHJpbSgpID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtWzlweF0gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCB1cHBlcmNhc2UgdHJhY2tpbmctdGlnaHQgcHgtMiBweS0wLjUgYmctZ3JheS01MFwiPlJlY2VudCBTZWFyY2hlczwvaDM+XG4gICAgICAgICAgICAgIHtyZWNlbnRTZWFyY2hlcy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgIHJlY2VudFNlYXJjaGVzLm1hcCgoc2VhcmNoLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHgtMiBweS0yIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFF1ZXJ5KHNlYXJjaCl9PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRpbWVySWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNzAwXCI+e3NlYXJjaH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eyhlKSA9PiBkZWxldGVSZWNlbnRTZWFyY2goc2VhcmNoLCBlKX0gY2xhc3NOYW1lPVwicC0xIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxYbWFya0ljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApIDogPGRpdiBjbGFzc05hbWU9XCJweC0yIHB5LTMgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+Tm8gcmVjZW50IHNlYXJjaGVzPC9kaXY+fVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IHJlc3VsdHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHNtOnB4LTYgcHktNiBzbTpweS04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBmbGV4IGgtMTAgdy0xMCBzbTpoLTEyIHNtOnctMTIgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBiZy1ncmF5LTEwMCBtYi0yIHNtOm1iLTNcIj5cbiAgICAgICAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHNtOmgtNSBzbTp3LTUgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+Tm8gcmVzdWx0cyBmb3VuZDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+V2UgY291bGRuJ3QgZmluZCBhbnl0aGluZyBtYXRjaGluZyBcIntxdWVyeX1cIjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICBPYmplY3QuZW50cmllcyhncm91cFJlc3VsdHMocmVzdWx0cykpLm1hcCgoW2NhdGVnb3J5LCBpdGVtc10pID0+XG4gICAgICAgICAgICAgIGl0ZW1zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtjYXRlZ29yeX0+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1bOXB4XSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIHVwcGVyY2FzZSB0cmFja2luZy10aWdodCBweC0yIHNtOnB4LTMgcHktMC41IHNtOnB5LTEgYmctZ3JheS01MFwiPntjYXRlZ29yeX08L2gzPlxuICAgICAgICAgICAgICAgICAge2l0ZW1zLm1hcCgocmVzdWx0OiBTZWFyY2hSZXN1bHQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3Jlc3VsdC5pZH0gY2xhc3NOYW1lPVwicHgtMiBzbTpweC0zIHB5LTEuNSBzbTpweS0yIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1zdGFydCBnYXAtMS41IHNtOmdhcC0zXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZXN1bHRDbGljayhyZXN1bHQpfT5cbiAgICAgICAgICAgICAgICAgICAgICB7cmVzdWx0LmltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9e3Jlc3VsdC5pbWFnZX0gYWx0PXtyZXN1bHQubmFtZSB8fCAnJ30gY2xhc3NOYW1lPVwiaC02IHctNiBzbTpoLTcgc206dy03IHJvdW5kZWQtZnVsbCBvYmplY3QtY292ZXIgbXQtMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBtdC0wLjUgcC0xIHNtOnAtMS41IHJvdW5kZWQtbWQgc206cm91bmRlZC1sZyBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRJY29uRm9yU291cmNlKHJlc3VsdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtYmFzZWxpbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIaWdobGlnaHRlZENvbnRlbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ9e3Jlc3VsdC50aXRsZSB8fCByZXN1bHQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhpZ2hsaWdodD17cmVzdWx0LmhpZ2hsaWdodH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHF1ZXJ5PXtxdWVyeX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVzdWx0LnB1Ymxpc2hlZEF0ICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWwtMiB3aGl0ZXNwYWNlLW5vd3JhcCBoaWRkZW4gc206aW5saW5lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGlzdGFuY2VUb05vdyhuZXcgRGF0ZShyZXN1bHQucHVibGlzaGVkQXQpLCB7IGFkZFN1ZmZpeDogdHJ1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuY29udGVudCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTAuNSBzbTptdC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMCBsaW5lLWNsYW1wLTEgc206bGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhpZ2hsaWdodGVkQ29udGVudFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudD17cmVzdWx0LmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoaWdobGlnaHQ9e3Jlc3VsdC5oaWdobGlnaHR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBxdWVyeT17cXVlcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuc291cmNlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHNtOm10LTEuNVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0xIHB5LTAuNSByb3VuZGVkIHRleHQtWzlweF0gZm9udC1tZWRpdW0gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldFJlc3VsdFR5cGUocmVzdWx0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgKVxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7aXNMb2FkaW5nICYmIGN1cnJlbnRQYWdlID4gMSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTIgc206cHktMyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TG9hZGVyIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayB3LTMgaC0zIHNtOnctNCBzbTpoLTQgdGV4dC1ncmF5LTQwMCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBTZW50aW5lbCBlbGVtZW50IGZvciBpbmZpbml0ZSBzY3JvbGwgKi99XG4gICAgICAgICAgPGRpdiBpZD1cInNlYXJjaC1yZXN1bHRzLXNlbnRpbmVsXCIgY2xhc3NOYW1lPVwiaC00IHctZnVsbFwiPjwvZGl2PlxuXG4gICAgICAgICAgeyFpc0xvYWRpbmcgJiYgcmVzdWx0cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMiBzbTpweS0zIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIHtoYXNNb3JlID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICBTY3JvbGwgZm9yIG1vcmUgcmVzdWx0c1xuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICB7dG90YWxJdGVtcyA+IDAgPyBgU2hvd2luZyBhbGwgJHt0b3RhbEl0ZW1zfSByZXN1bHRzYCA6ICdObyBtb3JlIHJlc3VsdHMnfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgPC9EaWFsb2c+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJJbnB1dCIsInVzZVdvcmtzcGFjZSIsInVzZUF1dGgiLCJzZWFyY2hXb3Jrc3BhY2VzIiwidXNlUm91dGVyIiwiZm9ybWF0RGlzdGFuY2VUb05vdyIsImRlYm91bmNlIiwiTWFnbmlmeWluZ0dsYXNzSWNvbiIsIlRpbWVySWNvbiIsIlhtYXJrSWNvbiIsIkJvb2tJY29uIiwiVGFibGVJY29uIiwiVXNlckdyb3VwSWNvbiIsIkNsb2NrVGhyZWVJY29uIiwiTm90ZUljb24iLCJTcXVhcmVMaXN0SWNvbiIsIldpZmlTbGFzaEljb24iLCJMb2FkZXIiLCJWaWV3SWNvbiIsIlZpZXdUeXBlIiwiSGlnaGxpZ2h0ZWRDb250ZW50IiwiY29udGVudCIsImhpZ2hsaWdodCIsInF1ZXJ5IiwidHJpbSIsInNlYXJjaFRlcm0iLCJ0b0xvd2VyQ2FzZSIsImNvbnRlbnRMb3dlciIsInBhcnRzIiwibGFzdEluZGV4IiwiY3VycmVudEluZGV4IiwiaW5kZXhPZiIsInB1c2giLCJzdWJzdHJpbmciLCJtYXJrIiwiY2xhc3NOYW1lIiwibGVuZ3RoIiwic3RhcnQiLCJNYXRoIiwibWF4IiwiZW5kIiwibWluIiwiZ3JvdXBSZXN1bHRzIiwicmVzdWx0cyIsImdyb3VwZWQiLCJkYXRhYmFzZXMiLCJwYWdlcyIsInZpZXdzIiwiZG9jdW1lbnRzIiwibWVtYmVycyIsImZvckVhY2giLCJyZXN1bHQiLCJpbWFnZSIsInNvdXJjZSIsImRhdGFiYXNlSWQiLCJ2aWV3SWQiLCJkb2N1bWVudElkIiwicGFnZUlkIiwiU2VhcmNoTW9kYWwiLCJvbkNsb3NlIiwiZGVib3VuY2VUaW1lb3V0TVMiLCJ3b3Jrc3BhY2UiLCJzZXRRdWVyeSIsInNldFJlc3VsdHMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwicmVjZW50U2VhcmNoZXMiLCJzZXRSZWNlbnRTZWFyY2hlcyIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJoYXNNb3JlIiwic2V0SGFzTW9yZSIsInRvdGFsSXRlbXMiLCJzZXRUb3RhbEl0ZW1zIiwiY3VycmVudFF1ZXJ5Iiwic2V0Q3VycmVudFF1ZXJ5IiwidXJsIiwidG9rZW4iLCJyb3V0ZXIiLCJ3b3Jrc3BhY2VJZCIsImlkIiwicmVzdWx0c0NvbnRhaW5lclJlZiIsInNhdmVkU2VhcmNoZXMiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwiZ2V0RXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJhcGlFcnJvciIsInJlc3BvbnNlIiwiZGF0YSIsIm1lc3NhZ2UiLCJwZXJmb3JtU2VhcmNoIiwic2VhcmNoUXVlcnkiLCJwYWdlIiwiYXBwZW5kIiwiY3VycmVudFNlYXJjaFF1ZXJ5IiwiaXNTdWNjZXNzIiwibmV3UmVzdWx0cyIsInBhZ2luYXRpb24iLCJ0b3RhbFBhZ2VzIiwicHJldiIsImNvbnNvbGUiLCJkZWJvdW5jZWRTZWFyY2giLCJsb2FkTW9yZSIsImN1cnJlbnQiLCJvYnNlcnZlciIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwiZW50cmllcyIsImVudHJ5IiwiaXNJbnRlcnNlY3RpbmciLCJyb290Iiwicm9vdE1hcmdpbiIsInRocmVzaG9sZCIsInNlbnRpbmVsIiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsIm9ic2VydmUiLCJ1bm9ic2VydmUiLCJkaXNjb25uZWN0IiwiY2FuY2VsIiwiaGFuZGxlUmVzdWx0Q2xpY2siLCJwYXRoIiwic2F2ZVJlY2VudFNlYXJjaCIsInNlYXJjaCIsInVwZGF0ZWRTZWFyY2hlcyIsImZpbHRlciIsInMiLCJzbGljZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJkZWxldGVSZWNlbnRTZWFyY2giLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiZ2V0SWNvbkZvclNvdXJjZSIsImluY2x1ZGVzIiwidmlld1R5cGUiLCJ0eXBlIiwicmVjb3JkSWQiLCJ1bmRlZmluZWQiLCJnZXRSZXN1bHRUeXBlIiwiVGFibGUiLCJCb2FyZCIsIkRhc2hib2FyZCIsIkRvY3VtZW50IiwiRm9ybSIsIlN1bW1hcnlUYWJsZSIsIkxpc3RWaWV3Iiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImhpZGVDbG9zZUJ0biIsImRpdiIsInBsYWNlaG9sZGVyIiwibmFtZSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJhdXRvRm9jdXMiLCJyZWYiLCJzdHlsZSIsIndpZHRoIiwicCIsImgzIiwibWFwIiwiaW5kZXgiLCJvbkNsaWNrIiwic3BhbiIsImJ1dHRvbiIsIk9iamVjdCIsImNhdGVnb3J5IiwiaXRlbXMiLCJpbWciLCJzcmMiLCJhbHQiLCJ0aXRsZSIsInB1Ymxpc2hlZEF0IiwiRGF0ZSIsImFkZFN1ZmZpeCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/searchmodal.tsx\n"));

/***/ })

});