"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_board_board_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.css":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.css ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"8dfea94f5535\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbS11aS9tZW50aW9uSW5wdXQuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jdXN0b20tdWkvbWVudGlvbklucHV0LmNzcz81ZTVhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOGRmZWE5NGY1NTM1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Compute caret position relative to viewport.\n                let rect = currentRange.getBoundingClientRect();\n                // If the rectangle is all zeros, create a temporary marker to compute correct coordinates.\n                if (rect.width === 0 && rect.height === 0) {\n                    var _marker_parentNode;\n                    const marker = document.createElement(\"span\");\n                    marker.textContent = \"​\"; // zero width space\n                    currentRange.insertNode(marker);\n                    rect = marker.getBoundingClientRect();\n                    (_marker_parentNode = marker.parentNode) === null || _marker_parentNode === void 0 ? void 0 : _marker_parentNode.removeChild(marker);\n                    sel.removeAllRanges();\n                    sel.addRange(currentRange);\n                }\n            // Position will be calculated in useEffect to ensure proper positioning\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Smart positioning: check if dropdown should appear above or below input\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (inputRect) {\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250; // approximate max height of dropdown\n                    // If not enough space below (less than dropdown height), try to show above\n                    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {\n                        setShowAbove(true);\n                    } else {\n                        setShowAbove(false);\n                    }\n                }\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 569,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"absolute z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: showAbove ? {\n                    bottom: \"100%\",\n                    left: \"0\",\n                    marginBottom: \"2px\"\n                } : {\n                    top: \"100%\",\n                    left: \"0\",\n                    marginTop: \"2px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 597,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 585,\n                columnNumber: 17\n            }, this),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 568,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"P2/CRSNwihOzgUKHan2mPDJ45XA=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});