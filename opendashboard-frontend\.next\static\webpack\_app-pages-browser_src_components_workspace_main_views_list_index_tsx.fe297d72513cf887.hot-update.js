"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx":
/*!*************************************************************************************!*\
  !*** ./src/components/workspace/main/views/form/components/element/buttonGroup.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonGroupFieldArea: function() { return /* binding */ ButtonGroupFieldArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/buttonActionHelpers */ \"(app-pages-browser)/./src/utils/buttonActionHelpers.ts\");\n/* harmony import */ var _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/buttonAction */ \"(app-pages-browser)/./src/utils/buttonAction.ts\");\n/* __next_internal_client_entry_do_not_use__ ButtonGroupFieldArea auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ButtonGroupFieldArea = (props)=>{\n    _s();\n    const { updateRecordValues, deleteRecords, directDeleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    const { confirm, toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { token, user } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { sendMessage } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const inputDialog = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog)();\n    const { id, columnsMap, databaseId, values } = props;\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek)();\n    const column = columnsMap[id];\n    const buttons = column.buttons || [];\n    const database = databaseStore === null || databaseStore === void 0 ? void 0 : databaseStore[databaseId];\n    if (!database) {\n        return null;\n    }\n    const row = {\n        id: String(values.id || \"\"),\n        recordValues: values\n    };\n    const handleButtonClick = async (button)=>{\n        var _button_actions;\n        if (!(button === null || button === void 0 ? void 0 : (_button_actions = button.actions) === null || _button_actions === void 0 ? void 0 : _button_actions.length)) {\n            toast.info(\"This button has no actions configured\");\n            return;\n        }\n        const context = {\n            record: row,\n            database: database,\n            workspace: workspace,\n            token: token,\n            user: user,\n            databaseId,\n            // Manually add the parent record context if we are in a peek view\n            parentRecord: maybeRecord ? {\n                id: maybeRecord.recordInfo.record.id,\n                databaseId: maybeRecord.database.id\n            } : undefined\n        };\n        const services = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.createActionServices)({\n            updateRecordValues: updateRecordValues,\n            deleteRecords,\n            directDeleteRecords,\n            setPeekRecord: (recordId, databaseId)=>openRecord(recordId, databaseId),\n            confirm,\n            toast,\n            router,\n            forceRender,\n            sendMessage: (message)=>sendMessage(\"info\", \"action\", {\n                    message\n                })\n        }, inputDialog);\n        const urlsToOpen = [];\n        let actionSucceeded = true;\n        for (const action of button.actions){\n            const { success, result } = await (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.executeDeclarativeAction)(action, context, services, databaseStore);\n            if (!success) {\n                actionSucceeded = false;\n                break;\n            }\n            if (action.actionType === \"openUrl\" && (result === null || result === void 0 ? void 0 : result.url)) {\n                urlsToOpen.push(result.url);\n            }\n        }\n        if (urlsToOpen.length > 0) {\n            services.toast.success(\"Opening \".concat(urlsToOpen.length, \" URL(s)...\"));\n            urlsToOpen.forEach((url)=>{\n                window.open(url, \"_blank\", \"noopener,noreferrer\");\n            });\n        }\n        if (actionSucceeded) {} else {}\n        forceRender();\n    };\n    const buttonStates = buttons.map((button)=>({\n            button,\n            state: (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.evaluateButtonState)(button, values || {})\n        }));\n    const visibleButtons = buttons.filter((button)=>{\n        var _buttonStates_find;\n        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n        return (buttonState === null || buttonState === void 0 ? void 0 : buttonState.visible) !== false;\n    });\n    if (!visibleButtons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-2 button-group-container\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 button-group-container\",\n                children: visibleButtons.length === 1 ? (()=>{\n                    var _buttonStates_find;\n                    const button = visibleButtons[0];\n                    const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                    const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                    const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                        disabled: isDisabled || hasError,\n                        variant: \"outline\",\n                        className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                        children: [\n                            hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 19\n                            }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate\",\n                                children: button.label || \"Action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 35\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 15\n                    }, undefined);\n                })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1 flex-wrap sm:flex-nowrap w-full\",\n                    children: [\n                        (()=>{\n                            var _buttonStates_find;\n                            const button = visibleButtons[0];\n                            const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                            const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                            const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                disabled: isDisabled || hasError,\n                                variant: \"outline\",\n                                className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                                children: [\n                                    hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 21\n                                    }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"truncate\",\n                                        children: button.label || \"Action\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 17\n                            }, undefined);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"text-xs rounded-full p-1.5 h-auto\",\n                                        variant: \"outline\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"size-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-48 z-[99999]\",\n                                    sideOffset: 5,\n                                    children: visibleButtons.slice(1).map((button)=>{\n                                        var _buttonStates_find;\n                                        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                                        const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                                        const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                            disabled: isDisabled || hasError,\n                                            className: \"text-xs p-2 gap-2 flex items-center \".concat(hasError ? \"text-gray-600 cursor-not-allowed\" : isDisabled ? \"text-gray-400 cursor-not-allowed\" : \"\"),\n                                            children: [\n                                                hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 25\n                                                }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 25\n                                                }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate\",\n                                                    children: button.label || \"Action\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, button.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.Dialog, {\n                open: inputDialog.inputDialogOpen,\n                onOpenChange: inputDialog.setInputDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogTitle, {\n                                children: inputDialog.inputDialogTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: inputDialog.inputDialogMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    value: inputDialog.inputValue,\n                                    onChange: (e)=>inputDialog.setInputValue(e.target.value),\n                                    placeholder: \"Enter your input here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>inputDialog.setInputDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: inputDialog.handleInputSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ButtonGroupFieldArea, \"KAV3e7PZmvIF5Zhl7XwvdCaQeXk=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek\n    ];\n});\n_c = ButtonGroupFieldArea;\nvar _c;\n$RefreshReg$(_c, \"ButtonGroupFieldArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2Zvcm0vY29tcG9uZW50cy9lbGVtZW50L2J1dHRvbkdyb3VwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDUTtBQUN5RTtBQUMzRDtBQUNrQjtBQUVuQztBQUNBO0FBQ1E7QUFDVztBQUNyQjtBQUNDO0FBQ1M7QUFHbUQ7QUFDMUQ7QUFDc0Q7QUFDM0M7QUFDTDtBQU1mO0FBQ3VHO0FBRXJJLE1BQU02Qix1QkFBdUIsQ0FBQ0M7O0lBQ25DLE1BQU0sRUFBRUMsa0JBQWtCLEVBQUVDLGFBQWEsRUFBRUMsbUJBQW1CLEVBQUUsR0FBR3pCLDBEQUFRQTtJQUMzRSxNQUFNLEVBQUUwQixPQUFPLEVBQUVDLEtBQUssRUFBRSxHQUFHMUIsMERBQVFBO0lBQ25DLE1BQU0sRUFBRTJCLGFBQWEsRUFBRUMsU0FBUyxFQUFFLEdBQUczQixrRUFBWUE7SUFDakQsTUFBTSxFQUFFNEIsV0FBVyxFQUFFLEdBQUczQiw2RUFBY0E7SUFDdEMsTUFBTSxFQUFFNEIsS0FBSyxFQUFFQyxJQUFJLEVBQUUsR0FBRzVCLHdEQUFPQTtJQUMvQixNQUFNNkIsU0FBUzVCLDJEQUFTQTtJQUN4QixNQUFNLEVBQUU2QixXQUFXLEVBQUUsR0FBRzVCLG1FQUFZQTtJQUNwQyxNQUFNNkIsY0FBY3BCLGtFQUFjQTtJQUVsQyxNQUFNcUIsY0FBY25CLDJFQUFjQTtJQUVoQyxNQUFNLEVBQUVvQixFQUFFLEVBQUVDLFVBQVUsRUFBRUMsVUFBVSxFQUFFQyxNQUFNLEVBQUUsR0FBR2xCO0lBQy9DLE1BQU0sRUFBRW1CLFVBQVUsRUFBRSxHQUFHM0IsdUVBQWNBO0lBQ3JDLE1BQU00QixTQUFTSixVQUFVLENBQUNELEdBQUc7SUFDN0IsTUFBTU0sVUFBVUQsT0FBT0MsT0FBTyxJQUFJLEVBQUU7SUFFdEMsTUFBTUMsV0FBV2hCLDBCQUFBQSxvQ0FBQUEsYUFBZSxDQUFDVyxXQUFXO0lBQzVDLElBQUksQ0FBQ0ssVUFBVTtRQUNiLE9BQU87SUFDVDtJQUVBLE1BQU1DLE1BQU07UUFDVlIsSUFBSVMsT0FBT04sT0FBT0gsRUFBRSxJQUFJO1FBQ3hCVSxjQUFjUDtJQUNoQjtJQUVBLE1BQU1RLG9CQUFvQixPQUFPQztZQUMxQkE7UUFBTCxJQUFJLEVBQUNBLG1CQUFBQSw4QkFBQUEsa0JBQUFBLE9BQVFDLE9BQU8sY0FBZkQsc0NBQUFBLGdCQUFpQkUsTUFBTSxHQUFFO1lBQzVCeEIsTUFBTXlCLElBQUksQ0FBQztZQUNYO1FBQ0Y7UUFFQSxNQUFNQyxVQUF5QjtZQUM3QkMsUUFBUVQ7WUFDUkQsVUFBVUE7WUFDVmYsV0FBV0E7WUFDWEUsT0FBT0E7WUFDUEMsTUFBTUE7WUFDTk87WUFDQSxrRUFBa0U7WUFDbEVnQixjQUFjcEIsY0FBYztnQkFDMUJFLElBQUlGLFlBQVlxQixVQUFVLENBQUNGLE1BQU0sQ0FBQ2pCLEVBQUU7Z0JBQ3BDRSxZQUFZSixZQUFZUyxRQUFRLENBQUNQLEVBQUU7WUFDckMsSUFBSW9CO1FBQ047UUFFQSxNQUFNQyxXQUFXeEMsaUZBQW9CQSxDQUFDO1lBQ3BDSyxvQkFBb0JBO1lBQ3BCQztZQUNBQztZQUNBa0MsZUFBZSxDQUFDQyxVQUFrQnJCLGFBQXVCRSxXQUFXbUIsVUFBVXJCO1lBQzlFYjtZQUNBQztZQUNBTTtZQUNBSDtZQUNBSSxhQUFhLENBQUMyQixVQUFvQjNCLFlBQVksUUFBUSxVQUFVO29CQUFFMkI7Z0JBQVE7UUFDNUUsR0FBR3pCO1FBRUgsTUFBTTBCLGFBQXVCLEVBQUU7UUFDL0IsSUFBSUMsa0JBQWtCO1FBRXRCLEtBQUssTUFBTUMsVUFBVWYsT0FBT0MsT0FBTyxDQUFFO1lBQ25DLE1BQU0sRUFBRWUsT0FBTyxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNbEQscUZBQXdCQSxDQUFDZ0QsUUFBbUVYLFNBQVNLLFVBQVU5QjtZQUVqSixJQUFJLENBQUNxQyxTQUFTO2dCQUNaRixrQkFBa0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJQyxPQUFPRyxVQUFVLEtBQUssY0FBYUQsbUJBQUFBLDZCQUFBQSxPQUFRRSxHQUFHLEdBQUU7Z0JBQ2xETixXQUFXTyxJQUFJLENBQUNILE9BQU9FLEdBQUc7WUFDNUI7UUFDRjtRQUVBLElBQUlOLFdBQVdYLE1BQU0sR0FBRyxHQUFHO1lBQ3pCTyxTQUFTL0IsS0FBSyxDQUFDc0MsT0FBTyxDQUFDLFdBQTZCLE9BQWxCSCxXQUFXWCxNQUFNLEVBQUM7WUFDcERXLFdBQVdRLE9BQU8sQ0FBQ0YsQ0FBQUE7Z0JBQ2pCRyxPQUFPQyxJQUFJLENBQUNKLEtBQUssVUFBVTtZQUM3QjtRQUNGO1FBRUEsSUFBSUwsaUJBQWlCLENBQ3JCLE9BQU8sQ0FDUDtRQUVBakM7SUFDRjtJQUVBLE1BQU0yQyxlQUFlOUIsUUFBUStCLEdBQUcsQ0FBQ3pCLENBQUFBLFNBQVc7WUFDMUNBO1lBQ0EwQixPQUFPeEQsZ0ZBQW1CQSxDQUFDOEIsUUFBZ05ULFVBQVUsQ0FBQztRQUN4UDtJQUVBLE1BQU1vQyxpQkFBaUJqQyxRQUFRa0MsTUFBTSxDQUFDNUIsQ0FBQUE7WUFDaEJ3QjtRQUFwQixNQUFNSyxlQUFjTCxxQkFBQUEsYUFBYU0sSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHL0IsTUFBTSxDQUFDWixFQUFFLEtBQUtZLE9BQU9aLEVBQUUsZUFBbERvQyx5Q0FBQUEsbUJBQXFERSxLQUFLO1FBQzlFLE9BQU9HLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYUcsT0FBTyxNQUFLO0lBQ2xDO0lBRUEsSUFBSSxDQUFDTCxlQUFlekIsTUFBTSxFQUFFO1FBQzFCLHFCQUNFLDhEQUFDK0I7WUFBSUMsV0FBVTs7Ozs7O0lBR25CO0lBRUEscUJBQ0U7OzBCQUNFLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWlAsZUFBZXpCLE1BQU0sS0FBSyxJQUN6QixDQUFDO3dCQUVxQnNCO29CQURwQixNQUFNeEIsU0FBUzJCLGNBQWMsQ0FBQyxFQUFFO29CQUNoQyxNQUFNRSxlQUFjTCxxQkFBQUEsYUFBYU0sSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHL0IsTUFBTSxDQUFDWixFQUFFLEtBQUtZLE9BQU9aLEVBQUUsZUFBbERvQyx5Q0FBQUEsbUJBQXFERSxLQUFLO29CQUM5RSxNQUFNUyxXQUFXTixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFILEtBQUssTUFBS3ZELDZEQUFXQSxDQUFDaUUsS0FBSztvQkFDekQsTUFBTUMsYUFBYVIsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhSCxLQUFLLE1BQUt2RCw2REFBV0EsQ0FBQ21FLFFBQVE7b0JBRTlELHFCQUNFLDhEQUFDOUYseURBQU1BO3dCQUNMK0YsU0FBUyxJQUFNLENBQUNGLGNBQWMsQ0FBQ0YsWUFBWXBDLGtCQUFrQkM7d0JBQzdEd0MsVUFBVUgsY0FBY0Y7d0JBQ3hCTSxTQUFRO3dCQUNSUCxXQUFXLDJFQUlWLE9BSENDLFdBQVcsaUZBQ1hFLGFBQWEsaUZBQ2I7OzRCQUdERix5QkFDQyw4REFBQ3JGLHlGQUF1QkE7Z0NBQUNvRixXQUFVOzs7Ozs0Q0FDakNHLDJCQUNGLDhEQUFDdkYseUZBQXVCQTtnQ0FBQ29GLFdBQVU7Ozs7OzRDQUVuQ3RFLGtIQUFhQSxDQUFDb0M7MENBRUUsOERBQUMwQztnQ0FBS1IsV0FBVTswQ0FBWWxDLE9BQU8yQyxLQUFLLElBQUk7Ozs7Ozs7Ozs7OztnQkFHcEUscUJBRUEsOERBQUNWO29CQUFJQyxXQUFVOzt3QkFDWDtnQ0FFb0JWOzRCQURwQixNQUFNeEIsU0FBUzJCLGNBQWMsQ0FBQyxFQUFFOzRCQUNoQyxNQUFNRSxlQUFjTCxxQkFBQUEsYUFBYU0sSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHL0IsTUFBTSxDQUFDWixFQUFFLEtBQUtZLE9BQU9aLEVBQUUsZUFBbERvQyx5Q0FBQUEsbUJBQXFERSxLQUFLOzRCQUM5RSxNQUFNUyxXQUFXTixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFILEtBQUssTUFBS3ZELDZEQUFXQSxDQUFDaUUsS0FBSzs0QkFDekQsTUFBTUMsYUFBYVIsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhSCxLQUFLLE1BQUt2RCw2REFBV0EsQ0FBQ21FLFFBQVE7NEJBRTlELHFCQUNFLDhEQUFDOUYseURBQU1BO2dDQUNMK0YsU0FBUyxJQUFNLENBQUNGLGNBQWMsQ0FBQ0YsWUFBWXBDLGtCQUFrQkM7Z0NBQzdEd0MsVUFBVUgsY0FBY0Y7Z0NBQ3hCTSxTQUFRO2dDQUNSUCxXQUFXLDJFQUlWLE9BSENDLFdBQVcsaUZBQ1hFLGFBQWEsaUZBQ2I7O29DQUdERix5QkFDQyw4REFBQ3JGLHlGQUF1QkE7d0NBQUNvRixXQUFVOzs7OztvREFDakNHLDJCQUNGLDhEQUFDdkYseUZBQXVCQTt3Q0FBQ29GLFdBQVU7Ozs7O29EQUVuQ3RFLGtIQUFhQSxDQUFDb0M7a0RBRWhCLDhEQUFDMEM7d0NBQUtSLFdBQVU7a0RBQVlsQyxPQUFPMkMsS0FBSyxJQUFJOzs7Ozs7Ozs7Ozs7d0JBR2xEO3NDQUVBLDhEQUFDbEcsc0VBQVlBOzs4Q0FDWCw4REFBQ0csNkVBQW1CQTtvQ0FBQ2dHLE9BQU87OENBQzFCLDRFQUFDcEcseURBQU1BO3dDQUFDMEYsV0FBVTt3Q0FBb0NPLFNBQVE7a0RBQzVELDRFQUFDNUYsMEdBQWVBOzRDQUFDcUYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHL0IsOERBQUN4Riw2RUFBbUJBO29DQUFDbUcsT0FBTTtvQ0FBTVgsV0FBVTtvQ0FBaUJZLFlBQVk7OENBQ3JFbkIsZUFBZW9CLEtBQUssQ0FBQyxHQUFHdEIsR0FBRyxDQUFDLENBQUN6Qjs0Q0FDUndCO3dDQUFwQixNQUFNSyxlQUFjTCxxQkFBQUEsYUFBYU0sSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHL0IsTUFBTSxDQUFDWixFQUFFLEtBQUtZLE9BQU9aLEVBQUUsZUFBbERvQyx5Q0FBQUEsbUJBQXFERSxLQUFLO3dDQUM5RSxNQUFNUyxXQUFXTixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFILEtBQUssTUFBS3ZELDZEQUFXQSxDQUFDaUUsS0FBSzt3Q0FDekQsTUFBTUMsYUFBYVIsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhSCxLQUFLLE1BQUt2RCw2REFBV0EsQ0FBQ21FLFFBQVE7d0NBRTlELHFCQUNFLDhEQUFDM0YsMEVBQWdCQTs0Q0FFZjRGLFNBQVMsSUFBTSxDQUFDRixjQUFjLENBQUNGLFlBQVlwQyxrQkFBa0JDOzRDQUM3RHdDLFVBQVVILGNBQWNGOzRDQUN4QkQsV0FBVyx1Q0FJVixPQUhDQyxXQUFXLHFDQUNYRSxhQUFhLHFDQUNiOztnREFHREYseUJBQ0MsOERBQUNyRix5RkFBdUJBO29EQUFDb0YsV0FBVTs7Ozs7Z0VBQ2pDRywyQkFDRiw4REFBQ3ZGLHlGQUF1QkE7b0RBQUNvRixXQUFVOzs7OztnRUFFbkN0RSxrSEFBYUEsQ0FBQ29DOzhEQUVoQiw4REFBQzBDO29EQUFLUixXQUFVOzhEQUFZbEMsT0FBTzJDLEtBQUssSUFBSTs7Ozs7OzsyQ0FoQnZDM0MsT0FBT1osRUFBRTs7Ozs7b0NBbUJwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT1YsOERBQUM5QiwwREFBTUE7Z0JBQUNpRSxNQUFNcEMsWUFBWTZELGVBQWU7Z0JBQUVDLGNBQWM5RCxZQUFZK0Qsa0JBQWtCOzBCQUNyRiw0RUFBQzNGLGlFQUFhQTs7c0NBQ1osOERBQUNFLGdFQUFZQTtzQ0FDWCw0RUFBQ0MsK0RBQVdBOzBDQUFFeUIsWUFBWWdFLGdCQUFnQjs7Ozs7Ozs7Ozs7c0NBRTVDLDhEQUFDbEI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDa0I7b0NBQUVsQixXQUFVOzhDQUE4Qi9DLFlBQVlrRSxrQkFBa0I7Ozs7Ozs4Q0FDekUsOERBQUMxRix3REFBS0E7b0NBQ0oyRixPQUFPbkUsWUFBWW9FLFVBQVU7b0NBQzdCQyxVQUFVLENBQUNDLElBQU10RSxZQUFZdUUsYUFBYSxDQUFDRCxFQUFFRSxNQUFNLENBQUNMLEtBQUs7b0NBQ3pETSxhQUFZOzs7Ozs7Ozs7Ozs7c0NBR2hCLDhEQUFDcEcsZ0VBQVlBOzs4Q0FDWCw4REFBQ2hCLHlEQUFNQTtvQ0FBQ2lHLFNBQVE7b0NBQVVvQixNQUFLO29DQUFLM0IsV0FBVTtvQ0FBVUssU0FBUyxJQUFNcEQsWUFBWStELGtCQUFrQixDQUFDOzhDQUFROzs7Ozs7OENBQzlHLDhEQUFDMUcseURBQU1BO29DQUFDaUcsU0FBUTtvQ0FBVW9CLE1BQUs7b0NBQUszQixXQUFVO29DQUFVSyxTQUFTcEQsWUFBWTJFLGlCQUFpQjs4Q0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU01RyxFQUFFO0dBdE9XMUY7O1FBQ3dEckIsc0RBQVFBO1FBQ2hEQyxzREFBUUE7UUFDRUMsOERBQVlBO1FBQ3pCQyx5RUFBY0E7UUFDZEMsb0RBQU9BO1FBQ2hCQyx1REFBU0E7UUFDQUMsK0RBQVlBO1FBQ2hCUyw4REFBY0E7UUFFZEUsdUVBQWNBO1FBR1RILG1FQUFjQTs7O0tBYjVCTyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy9mb3JtL2NvbXBvbmVudHMvZWxlbWVudC9idXR0b25Hcm91cC50c3g/NzU3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xyXG5pbXBvcnQgeyBEcm9wZG93bk1lbnUsIERyb3Bkb3duTWVudUNvbnRlbnQsIERyb3Bkb3duTWVudUl0ZW0sIERyb3Bkb3duTWVudVRyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnVcIjtcclxuaW1wb3J0IHsgQ2hldnJvbkRvd25JY29uIH0gZnJvbSBcIkBoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZVwiO1xyXG5pbXBvcnQgeyBUcmlhbmdsZUV4Y2xhbWF0aW9uSWNvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvaWNvbnMvRm9udEF3ZXNvbWVSZWd1bGFyXCI7XHJcbmltcG9ydCB7IFJlcXVpcmVkQXN0ZXJpc2sgfSBmcm9tIFwiQC9jb21wb25lbnRzL2N1c3RvbS11aS9yZXF1aXJlZEFzdGVyaXNrXCI7XHJcbmltcG9ydCB7IHVzZVZpZXdzIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3ZpZXdzXCI7XHJcbmltcG9ydCB7IHVzZUFsZXJ0IH0gZnJvbSBcIkAvcHJvdmlkZXJzL2FsZXJ0XCI7XHJcbmltcG9ydCB7IHVzZVdvcmtzcGFjZSB9IGZyb20gXCJAL3Byb3ZpZGVycy93b3Jrc3BhY2VcIjtcclxuaW1wb3J0IHVzZUZvcmNlUmVuZGVyIGZyb20gXCJAL2NvbXBvbmVudHMvY3VzdG9tLXVpL2ZvcmNlUmVuZGVyXCI7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9wcm92aWRlcnMvdXNlclwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCB7IHVzZUJyb2FkY2FzdCB9IGZyb20gXCJAL3Byb3ZpZGVycy9icm9hZGNhc3RcIjtcclxuaW1wb3J0IHsgQnV0dG9uR3JvdXBDb2x1bW4sIEFjdGlvbkJ1dHRvbiwgRGJDb25kaXRpb24sIERiUmVjb3JkRmlsdGVyIH0gZnJvbSBcIm9wZW5kYi1hcHAtZGItdXRpbHMvbGliL3R5cGluZ3MvZGJcIjtcclxuaW1wb3J0IHsgRm9ybUZpZWxkQXJlYVByb3BzIH0gZnJvbSBcIi4vdGV4dFwiO1xyXG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0Zvb3RlciwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCI7XHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xyXG5pbXBvcnQgeyBnZXRCdXR0b25JY29uIH0gZnJvbSBcIkAvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy90YWJsZS9yZW5kZXJlci9maWVsZHMvYnV0dG9uR3JvdXBcIjtcclxuaW1wb3J0IHsgdXNlU3RhY2tlZFBlZWsgfSBmcm9tIFwiQC9wcm92aWRlcnMvc3RhY2tlZFBlZWtcIjtcclxuaW1wb3J0IHsgdXNlTWF5YmVSZWNvcmQgfSBmcm9tIFwiQC9wcm92aWRlcnMvcmVjb3JkXCI7XHJcbmltcG9ydCB7IFxyXG4gIGV4ZWN1dGVEZWNsYXJhdGl2ZUFjdGlvbiwgXHJcbiAgdXNlSW5wdXREaWFsb2csIFxyXG4gIGNyZWF0ZUFjdGlvblNlcnZpY2VzLFxyXG4gIGV2YWx1YXRlQnV0dG9uU3RhdGUgXHJcbn0gZnJvbSBcIkAvdXRpbHMvYnV0dG9uQWN0aW9uSGVscGVyc1wiO1xyXG5pbXBvcnQgeyBCdXR0b25TdGF0ZSwgQWN0aW9uQ29udGV4dCwgQWN0aW9uU2VydmljZXMsIFJlY29yZERhdGEsIERhdGFiYXNlRGF0YSwgV29ya3NwYWNlRGF0YSwgVG9rZW5EYXRhLCBVc2VyIH0gZnJvbSBcIkAvdXRpbHMvYnV0dG9uQWN0aW9uXCI7XHJcblxyXG5leHBvcnQgY29uc3QgQnV0dG9uR3JvdXBGaWVsZEFyZWEgPSAocHJvcHM6IEZvcm1GaWVsZEFyZWFQcm9wcykgPT4ge1xyXG4gIGNvbnN0IHsgdXBkYXRlUmVjb3JkVmFsdWVzLCBkZWxldGVSZWNvcmRzLCBkaXJlY3REZWxldGVSZWNvcmRzIH0gPSB1c2VWaWV3cygpO1xyXG4gIGNvbnN0IHsgY29uZmlybSwgdG9hc3QgfSA9IHVzZUFsZXJ0KCk7XHJcbiAgY29uc3QgeyBkYXRhYmFzZVN0b3JlLCB3b3Jrc3BhY2UgfSA9IHVzZVdvcmtzcGFjZSgpO1xyXG4gIGNvbnN0IHsgZm9yY2VSZW5kZXIgfSA9IHVzZUZvcmNlUmVuZGVyKCk7XHJcbiAgY29uc3QgeyB0b2tlbiwgdXNlciB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHsgc2VuZE1lc3NhZ2UgfSA9IHVzZUJyb2FkY2FzdCgpO1xyXG4gIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKTtcclxuICBcclxuICBjb25zdCBpbnB1dERpYWxvZyA9IHVzZUlucHV0RGlhbG9nKCk7XHJcblxyXG4gICAgY29uc3QgeyBpZCwgY29sdW1uc01hcCwgZGF0YWJhc2VJZCwgdmFsdWVzIH0gPSBwcm9wcztcclxuICAgIGNvbnN0IHsgb3BlblJlY29yZCB9ID0gdXNlU3RhY2tlZFBlZWsoKTtcclxuICAgIGNvbnN0IGNvbHVtbiA9IGNvbHVtbnNNYXBbaWRdIGFzIEJ1dHRvbkdyb3VwQ29sdW1uO1xyXG4gICAgY29uc3QgYnV0dG9ucyA9IGNvbHVtbi5idXR0b25zIHx8IFtdO1xyXG5cclxuICBjb25zdCBkYXRhYmFzZSA9IGRhdGFiYXNlU3RvcmU/LltkYXRhYmFzZUlkXTtcclxuICBpZiAoIWRhdGFiYXNlKSB7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcblxyXG4gIGNvbnN0IHJvdyA9IHtcclxuICAgIGlkOiBTdHJpbmcodmFsdWVzLmlkIHx8IFwiXCIpLFxyXG4gICAgcmVjb3JkVmFsdWVzOiB2YWx1ZXMsXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQnV0dG9uQ2xpY2sgPSBhc3luYyAoYnV0dG9uOiBBY3Rpb25CdXR0b24pID0+IHtcclxuICAgIGlmICghYnV0dG9uPy5hY3Rpb25zPy5sZW5ndGgpIHtcclxuICAgICAgdG9hc3QuaW5mbyhcIlRoaXMgYnV0dG9uIGhhcyBubyBhY3Rpb25zIGNvbmZpZ3VyZWRcIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjb250ZXh0OiBBY3Rpb25Db250ZXh0ID0ge1xyXG4gICAgICByZWNvcmQ6IHJvdyBhcyB1bmtub3duIGFzIFJlY29yZERhdGEsXHJcbiAgICAgIGRhdGFiYXNlOiBkYXRhYmFzZSBhcyB1bmtub3duIGFzIERhdGFiYXNlRGF0YSxcclxuICAgICAgd29ya3NwYWNlOiB3b3Jrc3BhY2UgYXMgdW5rbm93biBhcyB7IHdvcmtzcGFjZT86IFdvcmtzcGFjZURhdGE7IGlkPzogc3RyaW5nOyBkb21haW4/OiBzdHJpbmcgfSxcclxuICAgICAgdG9rZW46IHRva2VuIGFzIFRva2VuRGF0YSxcclxuICAgICAgdXNlcjogdXNlciBhcyBVc2VyLFxyXG4gICAgICBkYXRhYmFzZUlkLFxyXG4gICAgICAvLyBNYW51YWxseSBhZGQgdGhlIHBhcmVudCByZWNvcmQgY29udGV4dCBpZiB3ZSBhcmUgaW4gYSBwZWVrIHZpZXdcclxuICAgICAgcGFyZW50UmVjb3JkOiBtYXliZVJlY29yZCA/IHtcclxuICAgICAgICBpZDogbWF5YmVSZWNvcmQucmVjb3JkSW5mby5yZWNvcmQuaWQsXHJcbiAgICAgICAgZGF0YWJhc2VJZDogbWF5YmVSZWNvcmQuZGF0YWJhc2UuaWQsXHJcbiAgICAgIH0gOiB1bmRlZmluZWRcclxuICAgIH07XHJcblxyXG4gICAgY29uc3Qgc2VydmljZXMgPSBjcmVhdGVBY3Rpb25TZXJ2aWNlcyh7XHJcbiAgICAgIHVwZGF0ZVJlY29yZFZhbHVlczogdXBkYXRlUmVjb3JkVmFsdWVzIGFzIChkYXRhYmFzZUlkOiBzdHJpbmcsIHJlY29yZElkczogc3RyaW5nW10sIHZhbHVlczogUmVjb3JkPHN0cmluZywgdW5rbm93bj4pID0+IFByb21pc2U8Ym9vbGVhbj4sXHJcbiAgICAgIGRlbGV0ZVJlY29yZHMsXHJcbiAgICAgIGRpcmVjdERlbGV0ZVJlY29yZHMsXHJcbiAgICAgIHNldFBlZWtSZWNvcmQ6IChyZWNvcmRJZDogc3RyaW5nLCBkYXRhYmFzZUlkOiBzdHJpbmcpID0+IG9wZW5SZWNvcmQocmVjb3JkSWQsIGRhdGFiYXNlSWQpLFxyXG4gICAgICBjb25maXJtLFxyXG4gICAgICB0b2FzdCxcclxuICAgICAgcm91dGVyLFxyXG4gICAgICBmb3JjZVJlbmRlcixcclxuICAgICAgc2VuZE1lc3NhZ2U6IChtZXNzYWdlOiBzdHJpbmcpID0+IHNlbmRNZXNzYWdlKCdpbmZvJywgJ2FjdGlvbicsIHsgbWVzc2FnZSB9KVxyXG4gICAgfSwgaW5wdXREaWFsb2cpO1xyXG5cclxuICAgIGNvbnN0IHVybHNUb09wZW46IHN0cmluZ1tdID0gW107XHJcbiAgICBsZXQgYWN0aW9uU3VjY2VlZGVkID0gdHJ1ZTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IGFjdGlvbiBvZiBidXR0b24uYWN0aW9ucykge1xyXG4gICAgICBjb25zdCB7IHN1Y2Nlc3MsIHJlc3VsdCB9ID0gYXdhaXQgZXhlY3V0ZURlY2xhcmF0aXZlQWN0aW9uKGFjdGlvbiBhcyB7IGFjdGlvblR5cGU6IHN0cmluZzsgcHJvcHM/OiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPiB9LCBjb250ZXh0LCBzZXJ2aWNlcywgZGF0YWJhc2VTdG9yZSk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoIXN1Y2Nlc3MpIHtcclxuICAgICAgICBhY3Rpb25TdWNjZWVkZWQgPSBmYWxzZTtcclxuICAgICAgICBicmVhazsgXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChhY3Rpb24uYWN0aW9uVHlwZSA9PT0gJ29wZW5VcmwnICYmIHJlc3VsdD8udXJsKSB7XHJcbiAgICAgICAgdXJsc1RvT3Blbi5wdXNoKHJlc3VsdC51cmwpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHVybHNUb09wZW4ubGVuZ3RoID4gMCkge1xyXG4gICAgICBzZXJ2aWNlcy50b2FzdC5zdWNjZXNzKGBPcGVuaW5nICR7dXJsc1RvT3Blbi5sZW5ndGh9IFVSTChzKS4uLmApO1xyXG4gICAgICB1cmxzVG9PcGVuLmZvckVhY2godXJsID0+IHtcclxuICAgICAgICB3aW5kb3cub3Blbih1cmwsICdfYmxhbmsnLCAnbm9vcGVuZXIsbm9yZWZlcnJlcicpO1xyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoYWN0aW9uU3VjY2VlZGVkKSB7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgfVxyXG5cclxuICAgIGZvcmNlUmVuZGVyKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgYnV0dG9uU3RhdGVzID0gYnV0dG9ucy5tYXAoYnV0dG9uID0+ICh7XHJcbiAgICBidXR0b24sXHJcbiAgICBzdGF0ZTogZXZhbHVhdGVCdXR0b25TdGF0ZShidXR0b24gYXMgeyBhY3Rpb25zPzogQXJyYXk8e2FjdGlvblR5cGU6IHN0cmluZzsgcHJvcHM/OiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPn0+OyB2aXNpYmxlSWY/OiBEYkNvbmRpdGlvbltdOyBlbmFibGVkSWY/OiBEYkNvbmRpdGlvbltdOyB2aXNpYmxlSWZGaWx0ZXI/OiBEYlJlY29yZEZpbHRlcjsgZW5hYmxlZElmRmlsdGVyPzogRGJSZWNvcmRGaWx0ZXIgfSwgdmFsdWVzIHx8IHt9KVxyXG4gIH0pKTtcclxuXHJcbiAgY29uc3QgdmlzaWJsZUJ1dHRvbnMgPSBidXR0b25zLmZpbHRlcihidXR0b24gPT4ge1xyXG4gICAgY29uc3QgYnV0dG9uU3RhdGUgPSBidXR0b25TdGF0ZXMuZmluZChicyA9PiBicy5idXR0b24uaWQgPT09IGJ1dHRvbi5pZCk/LnN0YXRlO1xyXG4gICAgcmV0dXJuIGJ1dHRvblN0YXRlPy52aXNpYmxlICE9PSBmYWxzZTsgXHJcbiAgfSk7XHJcblxyXG4gIGlmICghdmlzaWJsZUJ1dHRvbnMubGVuZ3RoKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIGJ1dHRvbi1ncm91cC1jb250YWluZXJcIj4gICBcclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTIgYnV0dG9uLWdyb3VwLWNvbnRhaW5lclwiPlxyXG4gICAgICAgIHt2aXNpYmxlQnV0dG9ucy5sZW5ndGggPT09IDEgPyAoXHJcbiAgICAgICAgICAoKCkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBidXR0b24gPSB2aXNpYmxlQnV0dG9uc1swXTtcclxuICAgICAgICAgICAgY29uc3QgYnV0dG9uU3RhdGUgPSBidXR0b25TdGF0ZXMuZmluZChicyA9PiBicy5idXR0b24uaWQgPT09IGJ1dHRvbi5pZCk/LnN0YXRlO1xyXG4gICAgICAgICAgICBjb25zdCBoYXNFcnJvciA9IGJ1dHRvblN0YXRlPy5zdGF0ZSA9PT0gQnV0dG9uU3RhdGUuRVJST1I7XHJcbiAgICAgICAgICAgIGNvbnN0IGlzRGlzYWJsZWQgPSBidXR0b25TdGF0ZT8uc3RhdGUgPT09IEJ1dHRvblN0YXRlLkRJU0FCTEVEO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiAhaXNEaXNhYmxlZCAmJiAhaGFzRXJyb3IgJiYgaGFuZGxlQnV0dG9uQ2xpY2soYnV0dG9uKX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0Rpc2FibGVkIHx8IGhhc0Vycm9yfVxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgaC1hdXRvIGZvbnQtc2VtaWJvbGQgZ2FwLTEgZmxleCBpdGVtcy1jZW50ZXIgJHtcclxuICAgICAgICAgICAgICAgICAgaGFzRXJyb3IgPyAnYmctZ3JheS01MCBob3ZlcjpiZy1ncmF5LTUwIGJvcmRlci1ncmF5LTIwMCB0ZXh0LWdyYXktNjAwIGN1cnNvci1ub3QtYWxsb3dlZCcgOlxyXG4gICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkID8gJ2JnLWdyYXktNTAgaG92ZXI6YmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTQwMCBjdXJzb3Itbm90LWFsbG93ZWQnIDpcclxuICAgICAgICAgICAgICAgICAgJydcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtoYXNFcnJvciA/IChcclxuICAgICAgICAgICAgICAgICAgPFRyaWFuZ2xlRXhjbGFtYXRpb25JY29uIGNsYXNzTmFtZT1cInNpemUtMyB0ZXh0LXJlZC01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgKSA6IGlzRGlzYWJsZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxUcmlhbmdsZUV4Y2xhbWF0aW9uSWNvbiBjbGFzc05hbWU9XCJzaXplLTMgdGV4dC1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIGdldEJ1dHRvbkljb24oYnV0dG9uKVxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlXCI+e2J1dHRvbi5sYWJlbCB8fCAnQWN0aW9uJ308L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICB9KSgpXHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgZmxleC13cmFwIHNtOmZsZXgtbm93cmFwIHctZnVsbFwiPlxyXG4gICAgICAgICAgICB7KCgpID0+IHtcclxuICAgICAgICAgICAgICBjb25zdCBidXR0b24gPSB2aXNpYmxlQnV0dG9uc1swXTtcclxuICAgICAgICAgICAgICBjb25zdCBidXR0b25TdGF0ZSA9IGJ1dHRvblN0YXRlcy5maW5kKGJzID0+IGJzLmJ1dHRvbi5pZCA9PT0gYnV0dG9uLmlkKT8uc3RhdGU7XHJcbiAgICAgICAgICAgICAgY29uc3QgaGFzRXJyb3IgPSBidXR0b25TdGF0ZT8uc3RhdGUgPT09IEJ1dHRvblN0YXRlLkVSUk9SO1xyXG4gICAgICAgICAgICAgIGNvbnN0IGlzRGlzYWJsZWQgPSBidXR0b25TdGF0ZT8uc3RhdGUgPT09IEJ1dHRvblN0YXRlLkRJU0FCTEVEO1xyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+ICFpc0Rpc2FibGVkICYmICFoYXNFcnJvciAmJiBoYW5kbGVCdXR0b25DbGljayhidXR0b24pfVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEaXNhYmxlZCB8fCBoYXNFcnJvcn1cclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSBoLWF1dG8gZm9udC1zZW1pYm9sZCBnYXAtMSBmbGV4IGl0ZW1zLWNlbnRlciAke1xyXG4gICAgICAgICAgICAgICAgICAgIGhhc0Vycm9yID8gJ2JnLWdyYXktNTAgaG92ZXI6YmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTYwMCBjdXJzb3Itbm90LWFsbG93ZWQnIDpcclxuICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkID8gJ2JnLWdyYXktNTAgaG92ZXI6YmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTQwMCBjdXJzb3Itbm90LWFsbG93ZWQnIDpcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2hhc0Vycm9yID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxUcmlhbmdsZUV4Y2xhbWF0aW9uSWNvbiBjbGFzc05hbWU9XCJzaXplLTMgdGV4dC1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgKSA6IGlzRGlzYWJsZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRyaWFuZ2xlRXhjbGFtYXRpb25JY29uIGNsYXNzTmFtZT1cInNpemUtMyB0ZXh0LXJlZC01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgIGdldEJ1dHRvbkljb24oYnV0dG9uKVxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPntidXR0b24ubGFiZWwgfHwgJ0FjdGlvbid9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfSkoKX1cclxuXHJcbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnU+XHJcbiAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgIDxCdXR0b24gY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgaC1hdXRvXCIgdmFyaWFudD1cIm91dGxpbmVcIj5cclxuICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJzaXplLTNcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGFsaWduPVwiZW5kXCIgY2xhc3NOYW1lPVwidy00OCB6LVs5OTk5OV1cIiBzaWRlT2Zmc2V0PXs1fT5cclxuICAgICAgICAgICAgICAgIHt2aXNpYmxlQnV0dG9ucy5zbGljZSgxKS5tYXAoKGJ1dHRvbikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBidXR0b25TdGF0ZSA9IGJ1dHRvblN0YXRlcy5maW5kKGJzID0+IGJzLmJ1dHRvbi5pZCA9PT0gYnV0dG9uLmlkKT8uc3RhdGU7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc0Vycm9yID0gYnV0dG9uU3RhdGU/LnN0YXRlID09PSBCdXR0b25TdGF0ZS5FUlJPUjtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgaXNEaXNhYmxlZCA9IGJ1dHRvblN0YXRlPy5zdGF0ZSA9PT0gQnV0dG9uU3RhdGUuRElTQUJMRUQ7XHJcbiAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2J1dHRvbi5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+ICFpc0Rpc2FibGVkICYmICFoYXNFcnJvciAmJiBoYW5kbGVCdXR0b25DbGljayhidXR0b24pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGlzYWJsZWQgfHwgaGFzRXJyb3J9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIHAtMiBnYXAtMiBmbGV4IGl0ZW1zLWNlbnRlciAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBoYXNFcnJvciA/ICd0ZXh0LWdyYXktNjAwIGN1cnNvci1ub3QtYWxsb3dlZCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkID8gJ3RleHQtZ3JheS00MDAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7aGFzRXJyb3IgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUcmlhbmdsZUV4Y2xhbWF0aW9uSWNvbiBjbGFzc05hbWU9XCJzaXplLTMgdGV4dC1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiBpc0Rpc2FibGVkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJpYW5nbGVFeGNsYW1hdGlvbkljb24gY2xhc3NOYW1lPVwic2l6ZS0zIHRleHQtcmVkLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBnZXRCdXR0b25JY29uKGJ1dHRvbilcclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPntidXR0b24ubGFiZWwgfHwgJ0FjdGlvbid9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51Q29udGVudD5cclxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnU+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxEaWFsb2cgb3Blbj17aW5wdXREaWFsb2cuaW5wdXREaWFsb2dPcGVufSBvbk9wZW5DaGFuZ2U9e2lucHV0RGlhbG9nLnNldElucHV0RGlhbG9nT3Blbn0+XHJcbiAgICAgICAgPERpYWxvZ0NvbnRlbnQ+XHJcbiAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgICA8RGlhbG9nVGl0bGU+e2lucHV0RGlhbG9nLmlucHV0RGlhbG9nVGl0bGV9PC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS00XCI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtYi00XCI+e2lucHV0RGlhbG9nLmlucHV0RGlhbG9nTWVzc2FnZX08L3A+XHJcbiAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgIHZhbHVlPXtpbnB1dERpYWxvZy5pbnB1dFZhbHVlfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaW5wdXREaWFsb2cuc2V0SW5wdXRWYWx1ZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGlucHV0IGhlcmVcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxyXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiIG9uQ2xpY2s9eygpID0+IGlucHV0RGlhbG9nLnNldElucHV0RGlhbG9nT3BlbihmYWxzZSl9PkNhbmNlbDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiIG9uQ2xpY2s9e2lucHV0RGlhbG9nLmhhbmRsZUlucHV0U3VibWl0fT5TdWJtaXQ8L0J1dHRvbj5cclxuICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgPC9EaWFsb2c+XHJcbiAgICA8Lz5cclxuICApO1xyXG59OyBcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiRHJvcGRvd25NZW51IiwiRHJvcGRvd25NZW51Q29udGVudCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiQ2hldnJvbkRvd25JY29uIiwiVHJpYW5nbGVFeGNsYW1hdGlvbkljb24iLCJ1c2VWaWV3cyIsInVzZUFsZXJ0IiwidXNlV29ya3NwYWNlIiwidXNlRm9yY2VSZW5kZXIiLCJ1c2VBdXRoIiwidXNlUm91dGVyIiwidXNlQnJvYWRjYXN0IiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Zvb3RlciIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiSW5wdXQiLCJnZXRCdXR0b25JY29uIiwidXNlU3RhY2tlZFBlZWsiLCJ1c2VNYXliZVJlY29yZCIsImV4ZWN1dGVEZWNsYXJhdGl2ZUFjdGlvbiIsInVzZUlucHV0RGlhbG9nIiwiY3JlYXRlQWN0aW9uU2VydmljZXMiLCJldmFsdWF0ZUJ1dHRvblN0YXRlIiwiQnV0dG9uU3RhdGUiLCJCdXR0b25Hcm91cEZpZWxkQXJlYSIsInByb3BzIiwidXBkYXRlUmVjb3JkVmFsdWVzIiwiZGVsZXRlUmVjb3JkcyIsImRpcmVjdERlbGV0ZVJlY29yZHMiLCJjb25maXJtIiwidG9hc3QiLCJkYXRhYmFzZVN0b3JlIiwid29ya3NwYWNlIiwiZm9yY2VSZW5kZXIiLCJ0b2tlbiIsInVzZXIiLCJyb3V0ZXIiLCJzZW5kTWVzc2FnZSIsIm1heWJlUmVjb3JkIiwiaW5wdXREaWFsb2ciLCJpZCIsImNvbHVtbnNNYXAiLCJkYXRhYmFzZUlkIiwidmFsdWVzIiwib3BlblJlY29yZCIsImNvbHVtbiIsImJ1dHRvbnMiLCJkYXRhYmFzZSIsInJvdyIsIlN0cmluZyIsInJlY29yZFZhbHVlcyIsImhhbmRsZUJ1dHRvbkNsaWNrIiwiYnV0dG9uIiwiYWN0aW9ucyIsImxlbmd0aCIsImluZm8iLCJjb250ZXh0IiwicmVjb3JkIiwicGFyZW50UmVjb3JkIiwicmVjb3JkSW5mbyIsInVuZGVmaW5lZCIsInNlcnZpY2VzIiwic2V0UGVla1JlY29yZCIsInJlY29yZElkIiwibWVzc2FnZSIsInVybHNUb09wZW4iLCJhY3Rpb25TdWNjZWVkZWQiLCJhY3Rpb24iLCJzdWNjZXNzIiwicmVzdWx0IiwiYWN0aW9uVHlwZSIsInVybCIsInB1c2giLCJmb3JFYWNoIiwid2luZG93Iiwib3BlbiIsImJ1dHRvblN0YXRlcyIsIm1hcCIsInN0YXRlIiwidmlzaWJsZUJ1dHRvbnMiLCJmaWx0ZXIiLCJidXR0b25TdGF0ZSIsImZpbmQiLCJicyIsInZpc2libGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoYXNFcnJvciIsIkVSUk9SIiwiaXNEaXNhYmxlZCIsIkRJU0FCTEVEIiwib25DbGljayIsImRpc2FibGVkIiwidmFyaWFudCIsInNwYW4iLCJsYWJlbCIsImFzQ2hpbGQiLCJhbGlnbiIsInNpZGVPZmZzZXQiLCJzbGljZSIsImlucHV0RGlhbG9nT3BlbiIsIm9uT3BlbkNoYW5nZSIsInNldElucHV0RGlhbG9nT3BlbiIsImlucHV0RGlhbG9nVGl0bGUiLCJwIiwiaW5wdXREaWFsb2dNZXNzYWdlIiwidmFsdWUiLCJpbnB1dFZhbHVlIiwib25DaGFuZ2UiLCJlIiwic2V0SW5wdXRWYWx1ZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwic2l6ZSIsImhhbmRsZUlucHV0U3VibWl0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx":
/*!************************************************************!*\
  !*** ./src/components/workspace/main/views/list/index.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: function() { return /* binding */ ListView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/checkbox */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/checkbox.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/providers/stackedPeek */ \"(app-pages-browser)/./src/providers/stackedPeek.tsx\");\n/* harmony import */ var _list_css__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./list.css */ \"(app-pages-browser)/./src/components/workspace/main/views/list/list.css\");\n/* __next_internal_client_entry_do_not_use__ ListView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListFieldRenderer = (param)=>{\n    let { field, row, databaseId } = param;\n    const meta = {\n        databaseId: databaseId,\n        column: field,\n        triggerEdit: false,\n        headerLocked: true,\n        contentLocked: true\n    };\n    const renderProps = {\n        column: {\n            key: field.id,\n            __meta__: meta,\n            idx: 0,\n            name: field.title,\n            frozen: false,\n            resizable: false,\n            sortable: false,\n            width: 150,\n            minWidth: 50,\n            maxWidth: undefined,\n            cellClass: undefined,\n            headerCellClass: undefined,\n            editable: false\n        },\n        row: row,\n        rowIdx: 0,\n        tabIndex: -1,\n        onRowChange: ()=>{},\n        isCellSelected: false,\n        selectCell: ()=>{},\n        isRowSelected: false\n    };\n    let RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n    switch(field.type){\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_11__.AIRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UUID:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.UUIDRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Number:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Text:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Derived:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_13__.LinkedRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Summarize:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_15__.SummarizeRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_12__.SelectRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Checkbox:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_7__.CheckboxRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Date:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_8__.DateRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy:\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_9__.PersonRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_10__.FileRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ScannableCode:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_17__.ScannableCodeRenderer;\n            break;\n        case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_16__.ButtonGroupRenderer;\n            break;\n        default:\n            RendererComponent = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextRenderer;\n    }\n    // @ts-ignore\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererComponent, {\n        ...renderProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 135,\n        columnNumber: 12\n    }, undefined);\n};\n_c = ListFieldRenderer;\nconst ListView = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo;\n    _s();\n    const { databaseStore, databaseErrorStore, members, workspace, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { cache, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViews)();\n    const { filter, sorts, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewSelection)();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_14__.usePage)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_19__.useMaybeShared)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.usePathname)();\n    const { openRecord } = (0,_providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__.useStackedPeek)();\n    const contentScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const horizontalScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_20__.useMaybeTemplate)();\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    // Sync horizontal scrolling between content and scrollbar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const contentElement = contentScrollRef.current;\n        const scrollbarElement = horizontalScrollRef.current;\n        if (!contentElement || !scrollbarElement) return;\n        const syncContentScroll = ()=>{\n            scrollbarElement.scrollLeft = contentElement.scrollLeft;\n        };\n        const syncScrollbarScroll = ()=>{\n            contentElement.scrollLeft = scrollbarElement.scrollLeft;\n        };\n        contentElement.addEventListener(\"scroll\", syncContentScroll);\n        scrollbarElement.addEventListener(\"scroll\", syncScrollbarScroll);\n        return ()=>{\n            contentElement.removeEventListener(\"scroll\", syncContentScroll);\n            scrollbarElement.removeEventListener(\"scroll\", syncScrollbarScroll);\n        };\n    }, []);\n    const getFieldsToDisplay = ()=>{\n        const fieldsToDisplay = [];\n        if (!database) return fieldsToDisplay;\n        const dbDefinition = database.database.definition;\n        if (!dbDefinition) return fieldsToDisplay;\n        let { columnsOrder, columnPropsMap } = definition;\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : [];\n        columnPropsMap = columnPropsMap || {};\n        for (const key of dbDefinition.columnIds){\n            if (!columnsOrder.includes(key)) columnsOrder.push(key);\n            if (!columnPropsMap[key]) columnPropsMap[key] = {};\n        }\n        for (const id of columnsOrder){\n            const dbCol = dbDefinition.columnsMap[id];\n            if (!dbCol) continue;\n            if (columnPropsMap[id].isHidden) continue;\n            fieldsToDisplay.push(dbCol);\n        }\n        return fieldsToDisplay;\n    };\n    const getProcessedRows = ()=>{\n        if (!database) return [];\n        const sortOptions = [];\n        if (sorts.length > 0) {\n            sortOptions.push(...sorts);\n        } else if (definition.sorts.length > 0) {\n            sortOptions.push(...definition.sorts);\n        }\n        if (sortOptions.length === 0) sortOptions.push({\n            columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.MagicColumn.CreatedAt,\n            order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Sort.Asc\n        });\n        const colIds = cache.getCache(\"newlyCreatedRecords\");\n        const createdColIds = colIds && Array.isArray(colIds) ? colIds : [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__.filterAndSortRecords)(database, members, databaseStore, definition.filter, filter, sortOptions, workspace.workspaceMember.userId, \"\", maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.id, createdColIds);\n        return rows;\n    };\n    const filteredRows = getProcessedRows();\n    const rows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_5__.searchFilteredRecords)(search, filteredRows);\n    const fieldsToDisplay = getFieldsToDisplay();\n    if (!database) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                lineNumber: 255,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 254,\n            columnNumber: 13\n        }, undefined);\n    }\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__.getDatabaseTitleCol)(database.database);\n    const isInRecordTab = !!maybeRecord;\n    const currentRecordId = maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id;\n    const isOnRecordPage = pathname.includes(\"/records/\") && pathname.endsWith(\"/records/\".concat(currentRecordId));\n    const handleRecordClick = (recordId, recordDatabaseId)=>{\n        if (definition.lockContent) return;\n        openRecord(recordId, recordDatabaseId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-hidden listView\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-hidden size-full flex flex-col\",\n            children: [\n                !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 border-b bg-yellow-50 text-xs text-center border-neutral-300 font-medium\",\n                    children: \"Content is locked, record navigation is disabled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden scroll-wrapper\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"content-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: contentScrollRef,\n                                className: \"content-horizontal-scroll\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"scroll-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-b rowGrid border-neutral-200 header-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold bg-white check !w-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold bg-white fluid\",\n                                                    children: \"Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-black font-bold bg-white\",\n                                                        children: field.title\n                                                    }, field.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 41\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        rows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No records found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 37\n                                        }, undefined) : rows.map((row)=>{\n                                            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_21__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts, database.database, members);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rowGrid border-b \".concat(definition.lockContent ? \"cursor-default\" : \"hover:bg-neutral-100 cursor-pointer\"),\n                                                onClick: (e)=>{\n                                                    const target = e.target;\n                                                    const isInteractiveField = target.closest('.r-button-group, .r-scannable-code, .r-files, button, [role=\"button\"]');\n                                                    console.log(\"Row click:\", {\n                                                        target: target.tagName,\n                                                        isInteractiveField,\n                                                        className: target.className\n                                                    });\n                                                    if (!isInteractiveField) {\n                                                        handleRecordClick(row.record.id, row.record.databaseId);\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs check !w-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs flex flex-col fluid\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"title-text text-xs font-semibold\",\n                                                                children: title || \"Untitled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 text-xs text-muted-foreground pt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: database.database.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 57\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_22__.timeAgo)(new Date(row.updatedAt))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 57\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs truncate\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListFieldRenderer, {\n                                                                field: field,\n                                                                row: row,\n                                                                databaseId: definition.databaseId,\n                                                                isPublishedView: isPublishedView,\n                                                                lockContent: definition.lockContent || false\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, field.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 53\n                                                        }, undefined))\n                                                ]\n                                            }, row.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 45\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: horizontalScrollRef,\n                            className: \"horizontal-scroll-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"horizontal-scroll-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rowGrid\",\n                                    style: {\n                                        visibility: \"hidden\",\n                                        height: \"1px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"check !w-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"fluid\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, field.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 277,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 276,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ListView, \"Ynk7SE4baYX8dCi41vGxGC0O2TY=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_4__.useViewSelection,\n        _providers_page__WEBPACK_IMPORTED_MODULE_14__.usePage,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_19__.useMaybeShared,\n        _providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord,\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.usePathname,\n        _providers_stackedPeek__WEBPACK_IMPORTED_MODULE_24__.useStackedPeek,\n        _providers_template__WEBPACK_IMPORTED_MODULE_20__.useMaybeTemplate\n    ];\n});\n_c1 = ListView;\nvar _c, _c1;\n$RefreshReg$(_c, \"ListFieldRenderer\");\n$RefreshReg$(_c1, \"ListView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx\n"));

/***/ })

});