"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick } = param;\n    // Create 60 minute segments for precise dropping\n    const minuteSegments = Array.from({\n        length: 60\n    }, (_, i)=>i);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col\",\n                children: minuteSegments.map((minute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinuteSegment, {\n                        day: day,\n                        hour: hour,\n                        minute: minute,\n                        style: {\n                            height: \"\".concat(100 / 60, \"%\"),\n                            top: \"\".concat(minute / 60 * 100, \"%\")\n                        },\n                        onDoubleClick: ()=>onDoubleClick(minute)\n                    }, minute, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TimeSlot;\n// New component for minute-level droppable segments\nconst MinuteSegment = (param)=>{\n    let { day, hour, minute, style, onDoubleClick } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour, \"-\").concat(minute),\n        data: {\n            date: day,\n            hour,\n            minute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute w-full\", isOver && \"bg-blue-50\"),\n        style: style,\n        onDoubleClick: onDoubleClick\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MinuteSegment, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = MinuteSegment;\nconst isMultiDayEvent = (event)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    // Reset time to compare dates only\n    const startDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());\n    const endDate = new Date(eventEnd.getFullYear(), eventEnd.getMonth(), eventEnd.getDate());\n    return startDate.getTime() !== endDate.getTime();\n};\nconst isAllDayEvent = (event)=>{\n    const start = new Date(event.start);\n    const end = new Date(event.end);\n    return start.getHours() === 0 && start.getMinutes() === 0 && end.getHours() === 23 && end.getMinutes() === 59;\n};\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.createEventSegments)(events);\n        return getSegmentsForWeek(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getAllDaySegments(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getTimeSlotSegments(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Filter events into all-day and regular events\n    const { allDaySegments: allDaySegmentsFromEvents, regularEvents } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return events.reduce((acc, event)=>{\n            if (isMultiDayEvent(event) || isAllDayEvent(event)) {\n                acc.allDaySegments.push(...(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.createEventSegments)(event));\n            } else {\n                acc.regularEvents.push(event);\n            }\n            return acc;\n        }, {\n            allDaySegments: [],\n            regularEvents: []\n        });\n    }, [\n        events\n    ]);\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 195,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = getSegmentsForDay(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = getSegmentHeight(layout.segment);\n                                                    const topOffset = getSegmentTopOffset(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 205,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegmentsFromEvents,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 316,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"0vr5Fu9pv3MA3eCfYj881ioWkCI=\");\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"MinuteSegment\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});