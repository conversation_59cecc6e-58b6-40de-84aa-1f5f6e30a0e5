"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=endOfWeek,format,isSameDay,isToday,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=endOfWeek,format,isSameDay,isToday,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=endOfWeek,format,isSameDay,isToday,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=endOfWeek,format,isSameDay,isToday,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=endOfWeek,format,isSameDay,isToday,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick, dropIndicator } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour,\n            type: \"timeslot\"\n        }\n    });\n    const showIndicator = dropIndicator && (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(dropIndicator.date, day) && new Date(dropIndicator.date).getHours() === hour;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\", isOver && \"bg-blue-50\"),\n        style: {\n            height: \"60px\"\n        },\n        onDoubleClick: ()=>onDoubleClick(0),\n        children: [\n            showIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute w-full bg-blue-500 opacity-75 pointer-events-none\",\n                style: {\n                    height: \"2px\",\n                    top: \"\".concat(dropIndicator.minute / 60 * 100, \"%\"),\n                    zIndex: 50\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData, dropIndicator } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>addDays(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(setHours(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                dropIndicator: dropIndicator,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 152,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_endOfWeek_format_isSameDay_isToday_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c1 = WeekView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});