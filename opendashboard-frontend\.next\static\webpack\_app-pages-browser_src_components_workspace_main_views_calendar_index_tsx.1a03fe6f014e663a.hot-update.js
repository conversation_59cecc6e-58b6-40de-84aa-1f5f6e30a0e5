"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CalendarEventItem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventItem.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CalendarSideCard */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarSideCard.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _components_workspace_main_views_ViewsRootLayout__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/ViewsRootLayout */ \"(app-pages-browser)/./src/components/workspace/main/views/ViewsRootLayout.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst DayCell = (param)=>{\n    let { date, children, onClick, isCurrentMonth } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable)({\n        id: \"daycell-\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(date, \"yyyy-MM-dd\")),\n        data: {\n            date: date,\n            type: \"daycell\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[170px] lg:p-3 lg:min-h-[190px]\", isCurrentMonth ? \"bg-white hover:bg-neutral-50\" : \"bg-neutral-100 hover:bg-neutral-200\", isOver && \"bg-blue-50 border-blue-200\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayCell, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_9__.useDroppable\n    ];\n});\n_c = DayCell;\n// Helper function to check if an event affects a specific day\nconst eventAffectsDay = (event, day)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    const dayStart = new Date(day);\n    const dayEnd = new Date(day);\n    dayEnd.setHours(23, 59, 59, 999);\n    return eventStart <= dayEnd && eventEnd >= dayStart;\n};\n// Helper function to check if an event is multi-day\nconst isMultiDayEvent = (event)=>{\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    // Reset time to compare dates only\n    const startDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());\n    const endDate = new Date(eventEnd.getFullYear(), eventEnd.getMonth(), eventEnd.getDate());\n    return startDate.getTime() !== endDate.getTime();\n};\nconst isAllDayEvent = (event)=>{\n    const start = new Date(event.start);\n    const end = new Date(event.end);\n    return start.getHours() === 0 && start.getMinutes() === 0 && end.getHours() === 23 && end.getMinutes() === 59;\n};\n// New helper function to process events for the entire month with proper slot tracking\nconst useMonthEvents = (weeks, events)=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const positionedEventsByWeek = new Map();\n        const slotUsageByDay = new Map(); // Track slot usage per day\n        weeks.forEach((week, weekIndex)=>{\n            const weekStart = week[0];\n            const weekEnd = new Date(week[6]);\n            weekEnd.setHours(23, 59, 59, 999);\n            // Get all events that intersect with this week\n            const weekEvents = events.filter((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return eventStart <= weekEnd && eventEnd >= weekStart;\n            });\n            const spanningEvents = [];\n            weekEvents.forEach((event)=>{\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                // Find which days this event spans in this week\n                const startDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventStart));\n                const endDayIndex = week.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, eventEnd));\n                // Calculate actual span within this week\n                const actualStart = startDayIndex !== -1 ? startDayIndex : 0;\n                const actualEnd = endDayIndex !== -1 ? endDayIndex : 6;\n                // Check if event intersects with this week\n                const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || eventStart < weekStart && eventEnd > weekEnd;\n                if (eventSpansWeek) {\n                    spanningEvents.push({\n                        event,\n                        startDayIndex: actualStart,\n                        endDayIndex: actualEnd,\n                        colSpan: actualEnd - actualStart + 1,\n                        isMultiDay: isMultiDayEvent(event),\n                        isAllDay: isAllDayEvent(event)\n                    });\n                }\n            });\n            // Sort events by start day, then by span length (longer events first)\n            const sortedEvents = spanningEvents.sort((a, b)=>{\n                if (a.startDayIndex !== b.startDayIndex) {\n                    return a.startDayIndex - b.startDayIndex;\n                }\n                return b.colSpan - a.colSpan;\n            });\n            // Position events and track slot usage\n            const positioned = [];\n            const rows = [];\n            sortedEvents.forEach((eventData)=>{\n                // Check if this event can be placed (all days it spans have available slots)\n                const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\n                const canPlace = affectedDays.every((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    return currentUsage < 4; // Maximum 4 slots per day\n                });\n                if (!canPlace) {\n                    // Event cannot be placed, skip it (it will be in the \"+more\" count)\n                    return;\n                }\n                // Find available row\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    const hasConflict = row.some((existingEvent)=>eventData.startDayIndex <= existingEvent.endDayIndex && eventData.endDayIndex >= existingEvent.startDayIndex);\n                    if (!hasConflict) {\n                        row.push(eventData);\n                        positioned.push({\n                            ...eventData,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        eventData\n                    ]);\n                    positioned.push({\n                        ...eventData,\n                        row: rows.length - 1\n                    });\n                }\n                // Update slot usage for all affected days\n                affectedDays.forEach((day)=>{\n                    const dayKey = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, \"yyyy-MM-dd\");\n                    const currentUsage = slotUsageByDay.get(dayKey) || 0;\n                    slotUsageByDay.set(dayKey, currentUsage + 1);\n                });\n            });\n            positionedEventsByWeek.set(weekIndex, positioned);\n        });\n        return {\n            positionedEventsByWeek,\n            slotUsageByDay\n        };\n    }, [\n        weeks,\n        events\n    ]);\n};\n_s1(useMonthEvents, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, activeDragData } = param;\n    _s2();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord)();\n    const isInRecordTab = !!maybeRecord;\n    // Memoize month calculations\n    const monthCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(selectedDate);\n        const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(selectedDate);\n        const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(monthStart, {\n            weekStartsOn: 0\n        });\n        const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(monthEnd, {\n            weekStartsOn: 0\n        });\n        const days = [];\n        let day = startDay;\n        while(day <= endDay){\n            days.push(day);\n            day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(day, 1);\n        }\n        const weeks = [];\n        for(let i = 0; i < days.length; i += 7){\n            weeks.push(days.slice(i, i + 7));\n        }\n        return {\n            monthStart,\n            monthEnd,\n            startDay,\n            endDay,\n            days,\n            weeks\n        };\n    }, [\n        selectedDate\n    ]);\n    // Memoize month events\n    const monthEvents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>events.filter((event)=>{\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            return eventStart <= monthCalculations.endDay && eventEnd >= monthCalculations.startDay;\n        }), [\n        events,\n        monthCalculations.startDay,\n        monthCalculations.endDay\n    ]);\n    const { positionedEventsByWeek } = useMonthEvents(monthCalculations.weeks, monthEvents);\n    const { context } = (0,_components_workspace_main_views_ViewsRootLayout__WEBPACK_IMPORTED_MODULE_10__.useViewContext)();\n    // Return empty state if no events\n    if (monthEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full bg-background flex flex-col lg:flex-row\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col min-h-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-b border-neutral-300 bg-white\",\n                            children: [\n                                \"Sunday\",\n                                \"Monday\",\n                                \"Tuesday\",\n                                \"Wednesday\",\n                                \"Thursday\",\n                                \"Friday\",\n                                \"Saturday\"\n                            ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2 text-xs font-medium text-gray-500 text-center\",\n                                    children: dayName\n                                }, dayName, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_7__.NoEvents, {\n                                title: \"No events this month\",\n                                message: \"\".concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, \"MMMM yyyy\"), \" is completely free. Start planning your month!\"),\n                                showCreateButton: canEditData,\n                                onCreate: ()=>openAddEventForm(selectedDate)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined),\n                context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                    selectedDate: selectedDate,\n                    events: events,\n                    selectedEvent: selectedEvent,\n                    setSelectedEvent: setSelectedEvent,\n                    handleEventClick: handleEventClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render day cell content\n    const renderDayCellContent = (day, dayEvents)=>{\n        const isToday = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, new Date());\n        const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium\", isToday && \"bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center\", !isCurrentMonth && \"text-gray-400\"),\n                            children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, undefined),\n                        canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            className: \"size-6 p-1 rounded-full items-center hover:bg-neutral-300 opacity-0 group-hover:opacity-100\",\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                const newDate = new Date(day);\n                                newDate.setHours(9, 0, 0, 0);\n                                openAddEventForm(newDate);\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"size-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: dayEvents.map((pe)=>{\n                        var _activeDragData_payload;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventItem__WEBPACK_IMPORTED_MODULE_6__.CalendarEventItem, {\n                            event: pe.event,\n                            style: {\n                                marginLeft: pe.startDayIndex === 0 ? \"0\" : \"-0.5rem\",\n                                marginRight: pe.endDayIndex === 6 ? \"0\" : \"-0.5rem\",\n                                width: pe.startDayIndex === 0 && pe.endDayIndex === 6 ? \"100%\" : pe.startDayIndex === 0 ? \"calc(100% + 0.5rem)\" : pe.endDayIndex === 6 ? \"calc(100% + 0.5rem)\" : \"calc(100% + 1rem)\"\n                            },\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                setSelectedEvent(pe.event.id);\n                                handleEventClick(pe.event);\n                            },\n                            view: \"month\",\n                            isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === pe.event.id,\n                            isDraggable: !pe.isMultiDay && !pe.isAllDay\n                        }, pe.event.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    // Render main view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-background flex flex-col lg:flex-row\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        \"data-day-headers\": \"true\",\n                        className: \"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-black\", \"py-2 text-xs\"),\n                                children: dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 border-neutral-300 border-b\",\n                            children: monthCalculations.weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                    const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\n                                    const dayEvents = weekEvents.filter((pe)=>pe.startDayIndex === dayIndex);\n                                    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                        date: day,\n                                        isCurrentMonth: isCurrentMonth,\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: renderDayCellContent(day, dayEvents)\n                                    }, \"\".concat(weekIndex, \"-\").concat(dayIndex), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarSideCard__WEBPACK_IMPORTED_MODULE_8__.CalendarSideCard, {\n                selectedDate: selectedDate,\n                events: events,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 355,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"7YEBKK+B/OAJfeDYNOuoWys6r8c=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_5__.useMaybeRecord,\n        useMonthEvents,\n        _components_workspace_main_views_ViewsRootLayout__WEBPACK_IMPORTED_MODULE_10__.useViewContext\n    ];\n});\n_c1 = MonthView;\nvar _c, _c1;\n$RefreshReg$(_c, \"DayCell\");\n$RefreshReg$(_c1, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});