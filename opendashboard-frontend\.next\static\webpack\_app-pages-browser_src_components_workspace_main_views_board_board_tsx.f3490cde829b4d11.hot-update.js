"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_board_board_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/common/workspaceNotes.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/workspace/main/common/workspaceNotes.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkspaceNotes: function() { return /* binding */ WorkspaceNotes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_workspace_main_common_YJSDoc__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/common/YJSDoc */ \"(app-pages-browser)/./src/components/workspace/main/common/YJSDoc.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/workspaceSocket */ \"(app-pages-browser)/./src/providers/workspaceSocket.tsx\");\n/* harmony import */ var _components_workspace_main_common_documentHistory__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/common/documentHistory */ \"(app-pages-browser)/./src/components/workspace/main/common/documentHistory.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WorkspaceNotes = (props)=>{\n    _s();\n    const { workspace, membersMap } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const perPage = 24;\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1);\n    const [load, setLoad] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [focusNote, setFocusNote] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const loadNotes = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        if (!token) return;\n        setLoad({\n            isLoading: true,\n            error: undefined\n        });\n        const params = {\n            perPage,\n            page\n        };\n        if (props.recordId && props.databaseId) {\n            params.type = \"record\";\n            params.recordId = props.recordId;\n            params.databaseId = props.databaseId;\n        } else {\n            params.type = \"user\";\n        }\n        const res = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_9__.getNotes)(token.token, workspace.workspace.id, params);\n        if (res.error) {\n            setLoad({\n                isLoading: false,\n                error: res.error\n            });\n            if (page > 1) toast.error(res.error);\n            return;\n        }\n        const oldData = (load === null || load === void 0 ? void 0 : load.data) || {\n            notes: []\n        };\n        if (page === 1) oldData.notes = [];\n        setLoad({\n            isLoading: false,\n            data: {\n                notes: [\n                    ...oldData.notes,\n                    ...res.data.data.notes\n                ]\n            }\n        });\n        setPage(page);\n        if (res.data.data.notes.length === 0 || page === 1 && res.data.data.notes.length < perPage) setHasMore(false);\n    };\n    const loadMore = ()=>{\n        loadNotes(page + 1);\n    };\n    const newNote = async ()=>{\n        var _load_data;\n        if (isCreating || !token) return;\n        const { recordId, databaseId } = props;\n        const res = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_9__.createNote)(token.token, workspace.workspace.id, {\n            recordId,\n            databaseId\n        });\n        setIsCreating(false);\n        if (res.error) {\n            toast.error(res.error);\n            return;\n        }\n        setLoad({\n            data: {\n                notes: [\n                    res.data.data.note,\n                    ...((_load_data = load.data) === null || _load_data === void 0 ? void 0 : _load_data.notes) || []\n                ]\n            }\n        });\n        setFocusNote(res.data.data.note);\n    };\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_19__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_19__.usePathname)();\n    const finishEdit = (note)=>{\n        setIsClosing(true);\n        if (!load.data) return;\n        const notes = [\n            ...load.data.notes\n        ];\n        for(let i = 0; i < notes.length; i++){\n            let note1 = load.data.notes[i];\n            if (note1.document.id === note.document.id) {\n                notes[i] = note;\n                break;\n            }\n        }\n        setLoad({\n            data: {\n                notes\n            }\n        });\n        setFocusNote(undefined);\n        if (noteIdParam) {\n            const params = new URLSearchParams();\n            searchParams.forEach((value, key)=>{\n                if (key !== \"noteId\") {\n                    params.append(key, value);\n                }\n            });\n            const newQuery = params.toString();\n            const newPath = pathname + (newQuery ? \"?\".concat(newQuery) : \"\");\n            router.replace(newPath, {\n                scroll: false\n            });\n            setTimeout(()=>{\n                setIsClosing(false);\n            }, 300);\n        } else {\n            setIsClosing(false);\n        }\n    };\n    const onDelete = (id)=>{\n        setIsClosing(true);\n        if (!load.data) return;\n        const notes = [\n            ...load.data.notes\n        ].filter((n)=>n.document.id !== id);\n        setLoad({\n            data: {\n                notes\n            }\n        });\n        setFocusNote(undefined);\n        if (noteIdParam && noteIdParam === id) {\n            const params = new URLSearchParams();\n            searchParams.forEach((value, key)=>{\n                if (key !== \"noteId\") {\n                    params.append(key, value);\n                }\n            });\n            const newQuery = params.toString();\n            const newPath = pathname + (newQuery ? \"?\".concat(newQuery) : \"\");\n            router.replace(newPath, {\n                scroll: false\n            });\n            setTimeout(()=>{\n                setIsClosing(false);\n            }, 300);\n        } else {\n            setIsClosing(false);\n        }\n    };\n    const { socket, isConnected } = (0,_providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_17__.useWorkspaceSocket)();\n    const loadRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(load);\n    loadRef.current = load;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!socket || !isConnected) return;\n        socket.on(\"note\", (d)=>{\n            var _loadRef_current_data;\n            console.log(\"New note callback\", d);\n            if (!loadRef.current.data) return;\n            if (d.note.document.recordId !== props.recordId || d.note.document.databaseId !== props.databaseId) return;\n            const notes = [\n                ...((_loadRef_current_data = loadRef.current.data) === null || _loadRef_current_data === void 0 ? void 0 : _loadRef_current_data.notes) || []\n            ];\n            const index = notes.findIndex((r)=>r.document.id === d.note.document.id);\n            if (index === -1) {\n                notes.unshift(d.note);\n            } else {\n                notes[index] = d.note;\n            }\n            setLoad({\n                ...loadRef.current,\n                data: {\n                    notes\n                }\n            });\n        });\n        socket.on(\"note-deleted\", (d)=>{\n            var _loadRef_current_data;\n            console.log(\"Deleted note callback\", d);\n            if (!loadRef.current.data) return;\n            if (d.note.document.recordId !== props.recordId || d.note.document.databaseId !== props.databaseId) return;\n            const notes = [\n                ...((_loadRef_current_data = loadRef.current.data) === null || _loadRef_current_data === void 0 ? void 0 : _loadRef_current_data.notes) || []\n            ].filter((r)=>r.document.id !== d.note.document.id);\n            setLoad({\n                ...loadRef.current,\n                data: {\n                    notes\n                }\n            });\n        });\n        console.log(\"Notes listener defined\");\n        return ()=>{\n            if (!socket) return;\n            socket.off(\"note\");\n            socket.off(\"note-deleted\");\n            console.log(\"Notes listener cleared\");\n        };\n    }, [\n        isConnected,\n        socket\n    ]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_19__.useSearchParams)();\n    const noteIdParam = searchParams.get(\"noteId\");\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setLoad({\n            data: undefined\n        });\n        loadNotes(1).then();\n    }, []);\n    const [isClosing, setIsClosing] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (noteIdParam && load.data && !isClosing && !focusNote) {\n            const noteToOpen = load.data.notes.find((note)=>note.document.id === noteIdParam);\n            if (noteToOpen) {\n                setFocusNote(noteToOpen);\n            }\n        }\n    }, [\n        noteIdParam,\n        load.data,\n        isClosing,\n        focusNote\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full w-full flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex p-4 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-semibold flex-1\",\n                            children: \"Notes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 17\n                        }, undefined),\n                        load.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            disabled: isCreating,\n                            onClick: newNote,\n                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                            children: \"New note\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        page === 1 && (load.isLoading || load.error || !load.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.PageLoader, {\n                                error: load.error,\n                                size: \"full\",\n                                cta: load.error ? {\n                                    \"label\": \"Retry\",\n                                    onClick: loadNotes\n                                } : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false),\n                        load.data && load.data.notes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_10__.PageLoader, {\n                                error: \"It's empty here\",\n                                size: \"full\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.NoteIcon, {\n                                    className: \"size-12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 31\n                                }, void 0),\n                                cta: {\n                                    \"label\": \"Create Note\",\n                                    onClick: newNote\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false),\n                        load.data && load.data.notes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_1__.ScrollArea, {\n                                className: \"w-full h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 pb-12 pt-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                            children: load.data.notes.map((n, i)=>{\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NoteRender, {\n                                                    workspaceId: workspace.workspace.id,\n                                                    onDelete: ()=>onDelete(n.document.id),\n                                                    n: n,\n                                                    onClick: ()=>setFocusNote(n),\n                                                    membersMap: membersMap\n                                                }, n.document.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 44\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center my-8 pb-16\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    disabled: load.isLoading,\n                                                    onClick: loadMore,\n                                                    className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                                                    children: load.isLoading ? \"Loading...\" : \"Load More\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 13\n                }, undefined),\n                focusNote && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditNote, {\n                    close: finishEdit,\n                    membersMap: membersMap,\n                    note: focusNote\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 27\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n            lineNumber: 221,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(WorkspaceNotes, \"kxo0Ji8Dnsffvjrt0Sg/4UEs89I=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert,\n        next_navigation__WEBPACK_IMPORTED_MODULE_19__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_19__.usePathname,\n        _providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_17__.useWorkspaceSocket,\n        next_navigation__WEBPACK_IMPORTED_MODULE_19__.useSearchParams\n    ];\n});\n_c = WorkspaceNotes;\nconst NoteRender = (param)=>{\n    let { n, onClick, membersMap, onDelete, workspaceId } = param;\n    var _member_user, _member_user1, _member_user2;\n    _s1();\n    const { confirm, toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const { token, user } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    let recordTitle = \"\";\n    let recordImage = \"\";\n    if (n.database && n.record) {\n        const { defaultTitle, titleColId, isContacts } = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_13__.getDatabaseTitleCol)(n.database);\n        recordTitle = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_13__.getRecordTitle)(n.record, titleColId, defaultTitle, isContacts, n.database);\n    }\n    const member = membersMap[n.document.createdById];\n    const memberName = \"\".concat((member === null || member === void 0 ? void 0 : (_member_user = member.user) === null || _member_user === void 0 ? void 0 : _member_user.firstName) || \"\", \" \").concat((member === null || member === void 0 ? void 0 : (_member_user1 = member.user) === null || _member_user1 === void 0 ? void 0 : _member_user1.lastName) || \"\").trim() || \"Unknown member\";\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const doDelete = async ()=>{\n        if (isDeleting || !token) return;\n        setIsDeleting(true);\n        const res = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_9__.deleteNote)(token.token, workspaceId, {\n            id: n.document.id\n        });\n        setIsDeleting(false);\n        if (res.error) {\n            toast.error(\"Error deleting note:\" + res.error);\n            return;\n        }\n        onDelete === null || onDelete === void 0 ? void 0 : onDelete();\n    };\n    const confirmDelete = ()=>{\n        confirm(\"Delete note?\", \"This cannot be reversed\", async ()=>{\n            doDelete().then();\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: onClick,\n            className: \"overflow-hidden p-3 border border-neutral-200 flex flex-col gap-2 hover:border-black transition-all relative select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        n.record && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"!p-0.5 gap-2 text-xs text-[10px] !h-auto hover:bg-neutral-200 truncate\",\n                                variant: \"ghost\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        className: \"size-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                className: \"size-full\",\n                                                src: recordImage\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                className: \"text-xs text-[10px]\",\n                                                children: (recordTitle || \"Untitled\")[0]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300\",\n                                        children: recordTitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 13\n                }, undefined),\n                (user === null || user === void 0 ? void 0 : user.id) === n.document.createdById && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-2 top-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"rounded-full h-auto p-1.5 size-6 text-xs gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.EllipsisVerticalIcon, {\n                                        className: \"size-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                className: \"w-28 rounded-none text-neutral-800 font-semibold\",\n                                align: \"end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                    disabled: isDeleting,\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        confirmDelete();\n                                    },\n                                    className: \"text-xs rounded-none p-2 flex gap-2\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 53\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs font-semibold truncate\",\n                    children: n.document.name || \"Untitled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-muted-foreground font-medium text-[10px] h-16 overflow-hidden\",\n                    children: n.document.contentText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 text-xs text-muted-foreground font-medium text-[10px] border-t pt-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"!p-0 gap-2 text-xs text-[10px] flex !h-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        className: \"size-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                className: \"size-full\",\n                                                src: member === null || member === void 0 ? void 0 : (_member_user2 = member.user) === null || _member_user2 === void 0 ? void 0 : _member_user2.profilePhoto\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                className: \"text-xs text-[10px]\",\n                                                children: memberName[0]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-[10px]\",\n                                        children: memberName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"Edited \",\n                                (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_15__.timeAgo)(new Date(n.document.createdAt))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n            lineNumber: 317,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s1(NoteRender, \"i0DEPJ6i3YQ434TLhAdU/tXzg3I=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c1 = NoteRender;\nconst EditNote = (props)=>{\n    _s2();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert)();\n    const [note, setNote] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(props.note);\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(props.note.document.name);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [editorReady, setIsEditorReady] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const roomName = \"n:\".concat(props.note.document.id, \"|\").concat(workspace.workspace.id);\n    let recordTitle = \"\";\n    let recordImage = \"\";\n    if (note.database && note.record) {\n        const { defaultTitle, titleColId, isContacts } = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_13__.getDatabaseTitleCol)(note.database);\n        recordTitle = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_13__.getRecordTitle)(note.record, titleColId, defaultTitle, isContacts, note.database);\n    }\n    const updateText = (text)=>{\n        setNote({\n            ...note,\n            document: {\n                ...note.document,\n                contentText: text\n            }\n        });\n    };\n    const updateTitle = async ()=>{\n        if (!token) return;\n        setIsSaving(true);\n        const res = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_9__.updateNote)(token.token, workspace.workspace.id, {\n            id: note.document.id,\n            name: title\n        });\n        setIsSaving(false);\n        if (res.error) {\n            toast.error(\"Error saving title: \" + res.error);\n            return;\n        }\n        setNote({\n            ...note,\n            document: {\n                ...note.document,\n                name: title,\n                updatedAt: new Date().toISOString()\n            }\n        });\n    };\n    const contentJSON = props.note.document.contentJSON;\n    const initialContent = Array.isArray(contentJSON) ? contentJSON : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n            open: true,\n            onOpenChange: ()=>props.close(note),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-w-[850px] h-3/4 !rounded-none p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"size-full overflow-hidden flex flex-col gap-0.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            className: \"h-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"font-bold text-xs flex gap-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-hidden\",\n                                        children: [\n                                            note.document.name || \"Untitled Note\",\n                                            note.record && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    \"in\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"!p-0.5 gap-2 text-xs text-[10px] ml-2 !h-auto hover:bg-neutral-200 truncate\",\n                                                        variant: \"ghost\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"size-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        className: \"size-full\",\n                                                                        src: recordImage\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 45\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"text-xs text-[10px]\",\n                                                                        children: (recordTitle || \"Untitled\")[0]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300\",\n                                                                children: recordTitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 41\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_documentHistory__WEBPACK_IMPORTED_MODULE_18__.DocumentHistoryModal, {\n                                        documentId: note.document.id,\n                                        membersMap: props.membersMap,\n                                        workspace: workspace.workspace,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            title: \"Version History\",\n                                            className: \"size-6 p-1.5 rounded-full mr-2 relative -top-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_12__.RectangleHistoryIcon, {\n                                                className: \"size-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 21\n                        }, undefined),\n                        editorReady && !connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_14__.ViewWarning, {\n                            message: _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_14__.WarningMessage.ConnectionLost\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 51\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col gap-2 py-2 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_1__.ScrollArea, {\n                                className: \"size-full scrollBlockChild\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1 w-full flex gap-1 items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            onBlur: (e)=>{\n                                                const val = e.target.value.trim();\n                                                if (val === note.document.name) return;\n                                                updateTitle();\n                                            },\n                                            onChange: (e)=>setTitle(e.target.value),\n                                            // readOnly={!!template}\n                                            className: \"text-2xl p-2 h-auto lg:p-8 lg:px-12 lg:pb-4 lg:h-18 font-black text-black border-none outline-none flex-1\",\n                                            placeholder: \"Untitled\",\n                                            value: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_YJSDoc__WEBPACK_IMPORTED_MODULE_6__.YJSDoc, {\n                                        documentId: \"WorkspaceNotes:\".concat(props.note.document.id),\n                                        roomName: roomName,\n                                        className: \"min-h-[calc(100%-100px)]\",\n                                        initialContent: initialContent,\n                                        onEditorReady: ()=>setIsEditorReady(true),\n                                        onConnectionStatusChanged: setConnected,\n                                        onChange: updateText,\n                                        collaborationEnabled: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n                lineNumber: 419,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\common\\\\workspaceNotes.tsx\",\n            lineNumber: 418,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s2(EditNote, \"t0F0aKaau+/q9MDMdquwAkb1ZSQ=\", false, function() {\n    return [\n        _providers_user__WEBPACK_IMPORTED_MODULE_8__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_11__.useAlert\n    ];\n});\n_c2 = EditNote;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WorkspaceNotes\");\n$RefreshReg$(_c1, \"NoteRender\");\n$RefreshReg$(_c2, \"EditNote\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/common/workspaceNotes.tsx\n"));

/***/ })

});