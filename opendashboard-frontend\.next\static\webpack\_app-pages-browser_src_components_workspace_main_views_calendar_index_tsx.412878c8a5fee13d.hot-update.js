"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CalendarEventSegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/CalendarEventSegment.tsx\");\n/* harmony import */ var _AllDayRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AllDayRow */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/AllDayRow.tsx\");\n/* harmony import */ var _NoEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoEvents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/NoEvents.tsx\");\n/* harmony import */ var _utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDayEventUtils */ \"(app-pages-browser)/./src/utils/multiDayEventUtils.ts\");\n/* harmony import */ var _utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollisionUtils */ \"(app-pages-browser)/./src/utils/eventCollisionUtils.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick, dropIndicator } = param;\n    _s();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"timeslot-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour,\n            type: \"timeslot\"\n        }\n    });\n    const showIndicator = dropIndicator && (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(dropIndicator.date, day) && new Date(dropIndicator.date).getHours() === hour;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\", isOver && \"bg-blue-50\"),\n        style: {\n            height: \"60px\"\n        },\n        onDoubleClick: ()=>onDoubleClick(0),\n        children: [\n            showIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute w-full bg-blue-500 opacity-75 pointer-events-none\",\n                style: {\n                    height: \"2px\",\n                    top: \"\".concat(dropIndicator.minute / 60 * 100, \"%\"),\n                    zIndex: 50\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData, dropIndicator } = param;\n    _s1();\n    // Memoize week-related calculations\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    // Memoize week segments\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    // Separate all-day and time-slot segments\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    // Memoize current time position\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    // Helper to get event duration in minutes\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Render empty state when no events\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoEvents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, undefined);\n    // Render time slots with events\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollisionUtils__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                dropIndicator: dropIndicator,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDayEventUtils__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CalendarEventSegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 152,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AllDayRow__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c1 = WeekView;\nvar _c, _c1;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});